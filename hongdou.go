package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/consumer"
	"github.com/run-bigpig/hongdou/internal/handler"
	"github.com/run-bigpig/hongdou/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/hongdou.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()
	parentCtx := context.Background()
	ctx := svc.NewServiceContext(parentCtx, c)
	//初始化消费者
	consumer.Init(parentCtx, ctx)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
