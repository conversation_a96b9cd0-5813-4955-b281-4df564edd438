package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/bot/feishu"
	"github.com/run-bigpig/hongdou/internal/config"
)

func main() {
	fmt.Println("Testing Bot Framework...")

	ctx := context.Background()

	// 创建飞书机器人配置
	feishuConfig := &config.FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
		WebhookURL: "https://test.com/webhook",
	}
	feishuConfig.DirectiveCard.TemplateId = "ctp_test123"
	feishuConfig.DirectiveCard.TemplateVersion = "1.0.0"

	// 创建飞书机器人实例
	feishuBot := feishu.NewFeishuBot(ctx, feishuConfig)

	// 测试机器人接口实现
	fmt.Printf("机器人平台: %s\n", feishuBot.GetPlatform())
	fmt.Printf("机器人健康状态: %v\n", feishuBot.IsHealthy())

	// 创建机器人管理器
	manager := bot.NewBotManager()

	// 注册飞书机器人
	err := manager.RegisterBot(bot.PlatformFeishu, feishuBot)
	if err != nil {
		log.Fatalf("注册飞书机器人失败: %v", err)
	}

	// 获取注册的机器人
	registeredBot, err := manager.GetBot(bot.PlatformFeishu)
	if err != nil {
		log.Fatalf("获取飞书机器人失败: %v", err)
	}

	fmt.Printf("成功获取机器人: %s\n", registeredBot.GetPlatform())

	// 测试事件处理
	testEvent := &bot.Event{
		Type:      "im.message.receive_v1",
		Platform:  bot.PlatformFeishu,
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"message": map[string]interface{}{
				"message_id":   "om_test123456",
				"message_type": "text",
				"chat_type":    "p2p",
				"chat_id":      "oc_test123456",
				"content":      `{"text": "你好，机器人！"}`,
				"create_time":  "**********",
				"mentions":     []interface{}{},
			},
			"sender": map[string]interface{}{
				"sender_id": map[string]interface{}{
					"open_id": "ou_test123456",
				},
				"sender_type": "user",
				"tenant_key":  "test_tenant",
			},
		},
	}

	fmt.Println("\n测试事件处理...")
	// 注意：这里会失败因为我们没有真实的飞书配置，但展示了框架的使用方式
	err = registeredBot.HandleEvent(ctx, testEvent)
	if err != nil {
		fmt.Printf("预期的错误（无真实配置）: %v\n", err)
	}

	// 创建事件处理器和消息处理器
	eventHandler := bot.NewEventHandler()
	messageProcessor := bot.NewMessageProcessor()

	// 创建机器人服务
	botService := bot.NewBotService(manager, eventHandler, messageProcessor)

	// 测试机器人状态
	fmt.Println("\n机器人状态:")
	status := botService.GetBotStatus()
	for platform, healthy := range status {
		fmt.Printf("- %s: %v\n", platform, healthy)
	}

	// 测试多机器人管理
	allBots := manager.GetAllBots()
	fmt.Printf("\n注册的机器人数量: %d\n", len(allBots))
	for platform, bot := range allBots {
		fmt.Printf("- 平台: %s, 健康状态: %v\n", platform, bot.IsHealthy())
	}

	// 清理资源
	err = manager.StopAll()
	if err != nil {
		fmt.Printf("停止机器人时出错: %v\n", err)
	}

	fmt.Println("\n机器人框架测试完成!")
	fmt.Println("框架特性:")
	fmt.Println("✓ 支持多平台机器人抽象")
	fmt.Println("✓ 统一的事件处理接口")
	fmt.Println("✓ 可扩展的消息处理器")
	fmt.Println("✓ 机器人生命周期管理")
	fmt.Println("✓ 健康状态监控")
	fmt.Println("✓ 配置验证和错误处理")
}
