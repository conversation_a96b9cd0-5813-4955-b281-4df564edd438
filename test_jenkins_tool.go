package main

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/run-bigpig/hongdou/internal/service/tools/trigger_jenkins_build"
)

func main() {
	fmt.Println("Testing Jenkins Build Trigger Tool...")

	// Create a Jenkins build trigger tool
	ctx := context.Background()
	jenkinsTool := trigger_jenkins_build.New(ctx)

	// Display tool information
	fmt.Printf("Tool Name: %s\n", jenkinsTool.Name())
	fmt.Printf("Tool Description: %s\n\n", jenkinsTool.Description())

	// Display parameters
	fmt.Println("Tool Parameters:")
	params := jenkinsTool.Parameters()
	for name, spec := range params {
		fmt.Printf("- %s (%s): %s [Required: %v]\n", 
			name, spec.Type, spec.Description, spec.Required)
	}

	// Test parameter validation with missing required parameters
	fmt.Println("\nTesting parameter validation...")
	
	// Test with empty input
	result, err := jenkinsTool.Run(ctx, "{}")
	if err != nil {
		fmt.Printf("✓ Expected error for missing parameters: %v\n", err)
	}

	// Test with valid parameters (but fake <PERSON> server)
	fmt.Println("\nTesting with sample parameters...")
	testParams := map[string]interface{}{
		"jenkins_url": "http://localhost:8080",
		"job_name":    "test-job",
		"username":    "admin",
		"token":       "test-token",
		"parameters": map[string]interface{}{
			"branch":      "master",
			"environment": "staging",
		},
	}

	jsonParams, _ := json.Marshal(testParams)
	fmt.Printf("Test parameters: %s\n", string(jsonParams))

	// This will fail because we don't have a real Jenkins server, but it demonstrates the tool structure
	result, err = jenkinsTool.Run(ctx, string(jsonParams))
	if err != nil {
		fmt.Printf("Expected error (no real Jenkins server): %v\n", err)
	} else {
		fmt.Printf("Result: %s\n", result)
	}

	fmt.Println("\nJenkins Build Trigger Tool test completed!")
	fmt.Println("The tool is ready for integration and can be used with real Jenkins servers.")
}
