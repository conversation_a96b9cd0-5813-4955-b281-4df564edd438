syntax = "v1"

type Empty {}

type ChatRequest {
	ConversationId string `json:"conversation_id"`
	Message        string `json:"message"`
	Stream         bool   `json:"stream,optional"`
}

type ChatResponse {
	Message string `json:"message"`
}

type StreamResponse {
	Type    string `json:"type"` // "progress", "result", "error"
	Content string `json:"content"`
	Done    bool   `json:"done"`
}

type SaveMemoryQuest {
	Content string `json:"content"`
}

type FeiShuBotEventHeader {
	EventId    string `json:"event_id"` //事件ID
	EventType  string `json:"event_type"` //事件类型
	CreateTime string `json:"create_time"` //事件创建时间戳 单位毫秒
	Token      string `json:"token"` //事件token
	AppId      string `json:"app_id"` //应用ID
	TenantKey  string `json:"tenant_key"` //租户Key
}

type FeiShuBotReceiveEventRequest {
	Schema string                 `json:"schema"` //事件模式
	Header FeiShuBotEventHeader   `json:"header"` //事件头
	Event  map[string]interface{} `json:"event"` //事件体
}

@server (
	prefix: /api/v1
)
service hongdou {
	@doc (
		summary: "聊天 - Unified endpoint supporting both streaming and non-streaming responses"
	)
	@handler ChatHandler
	post /chat (ChatRequest) returns (ChatResponse)

	@doc (
		summary: "保存长期记忆"
	)
	@handler SaveMemoryHandler
	post /save_memory (SaveMemoryQuest) returns (Empty)

	@doc (
		summary: "飞书机器人事件"
	)
	@handler FeiShuBotEventHandler
	post /feishu_bot_event (FeiShuBotReceiveEventRequest) returns (Empty)
}

