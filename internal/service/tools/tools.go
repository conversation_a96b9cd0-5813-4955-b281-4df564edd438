package tools

import (
	"context"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/mcp"
	"github.com/run-bigpig/hongdou/internal/service/tools/fetch_webpage"
	"github.com/run-bigpig/hongdou/internal/service/tools/get_aliyun_balance"
	"github.com/run-bigpig/hongdou/internal/service/tools/get_aws_billing"
	"github.com/run-bigpig/hongdou/internal/service/tools/get_mongo_slow_query"
	"github.com/run-bigpig/hongdou/internal/service/tools/get_weather"
	"github.com/run-bigpig/hongdou/internal/service/tools/trigger_jenkins_build"
	"github.com/zeromicro/go-zero/core/logx"
	"log"
	"strings"
)

type Tools struct {
	ctx             context.Context
	mcpConfig       config.McpServerConfig
	selfToolsConfig config.SelfToolsConfig
	logger          logx.Logger
	allTools        map[string]interfaces.Tool
	discovery       *ToolDiscovery
}

func NewTools(ctx context.Context, mcpConfig config.McpServerConfig, selfToolsConfig config.SelfToolsConfig) *Tools {
	tools := &Tools{
		ctx:             ctx,
		mcpConfig:       mcpConfig,
		selfToolsConfig: selfToolsConfig,
		logger:          logx.WithContext(ctx),
		allTools:        map[string]interfaces.Tool{},
	}
	tools.loadMcpTools()
	tools.loadSelfTools()
	return tools
}

// SetDiscovery sets the tool discovery service (called after embedder is available)
func (t *Tools) SetDiscovery(embedder interfaces.Embedder, vectorStore interfaces.VectorStore) {
	t.discovery = NewToolDiscovery(t.ctx, t, embedder, vectorStore)
}

func (t *Tools) loadMcpTools() {
	if t.mcpConfig.Host == "" {
		return
	}
	//载入mcp tools
	mcpHttpServer, err := mcp.NewHTTPServer(t.ctx, mcp.HTTPServerConfig{
		BaseURL: t.mcpConfig.Host,
	})
	if err != nil {
		log.Fatal(err)
	}
	mcpTools, err := mcpHttpServer.ListTools(t.ctx)
	if err != nil {
		t.logger.Errorf("获取mcp tools失败: %v", err)
		log.Fatal(err)
	}
	for _, mcpTool := range mcpTools {
		t.allTools[mcpTool.Name] = mcp.NewMCPTool(mcpTool.Name, mcpTool.Description, mcpTool.Schema, mcpHttpServer)
	}
}

func (t *Tools) loadSelfTools() {
	t.allTools["get_weather"] = get_weather.New(t.ctx)
	t.allTools["fetch_webpage"] = fetch_webpage.New(t.ctx)
	t.allTools["get_aws_billing"] = get_aws_billing.New(t.ctx, &t.selfToolsConfig.AwsConfig)
	t.allTools["get_mongo_slow_query"] = get_mongo_slow_query.New(t.ctx, &t.selfToolsConfig.AtlasConfig)
	t.allTools["get_aliyun_balance"] = get_aliyun_balance.New(t.ctx, &t.selfToolsConfig.AliyunConfig)
	t.allTools["trigger_jenkins_build"] = trigger_jenkins_build.New(t.ctx, &t.selfToolsConfig.JenkinsConfig)
}

func (t *Tools) List(names ...string) []interfaces.Tool {
	listTools := make([]interfaces.Tool, 0)
	for _, name := range names {
		tool, ok := t.allTools[name]
		if !ok {
			t.logger.Errorf("tool %s not found", name)
			continue
		}
		listTools = append(listTools, tool)
		t.logger.Infof("loaded tool %s: %s", name, tool.Description())
	}
	return listTools
}

// GetToolsContext returns a context string for discovered tools
func (t *Tools) GetToolsContext(query string, maxTools int) string {
	if t.discovery == nil {
		return t.simpleToolsContext(query, maxTools)
	}

	return t.discovery.GetToolsContext(query, maxTools)
}

// simpleToolMatch provides basic keyword matching when semantic discovery is not available
func (t *Tools) simpleToolMatch(query string, maxTools int) []interfaces.Tool {
	queryLower := strings.ToLower(query)
	matches := make([]interfaces.Tool, 0)

	for name, tool := range t.allTools {
		if strings.Contains(queryLower, strings.ToLower(name)) ||
			strings.Contains(strings.ToLower(tool.Description()), queryLower) {
			matches = append(matches, tool)
			if len(matches) >= maxTools {
				break
			}
		}
	}

	return matches
}

// simpleToolsContext provides basic tools context when semantic discovery is not available
func (t *Tools) simpleToolsContext(query string, maxTools int) string {
	tools := t.simpleToolMatch(query, maxTools)
	if len(tools) == 0 {
		return "No relevant tools found."
	}

	var ctx strings.Builder
	ctx.WriteString("Available tools:\n")
	for i, tool := range tools {
		ctx.WriteString(fmt.Sprintf("%d. %s - %s\n", i+1, t.getToolNameByInstance(tool), tool.Description()))
	}

	return ctx.String()
}

// getToolNameByInstance finds the name of a tool by its instance
func (t *Tools) getToolNameByInstance(targetTool interfaces.Tool) string {
	for name, tool := range t.allTools {
		if tool == targetTool {
			return name
		}
	}
	return "unknown"
}
