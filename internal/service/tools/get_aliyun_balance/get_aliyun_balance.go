package get_aliyun_balance

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/bssopenapi"
	config2 "github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/zeromicro/go-zero/core/logx"
)

// AliyunBalanceTool 实现了 Tool 接口，提供阿里云账户余额查询功能
type AliyunBalanceTool struct {
	ctx             context.Context
	logger          logx.Logger
	accessKeyId     string
	accessKeySecret string
}

func New(ctx context.Context, aliyunConfig *config2.AliyunConfig) *AliyunBalanceTool {
	return &AliyunBalanceTool{
		ctx:             ctx,
		logger:          logx.WithContext(ctx),
		accessKeyId:     aliyunConfig.AccessKeyId,
		accessKeySecret: aliyunConfig.AccessKeySecret,
	}
}

// Name 返回工具名称
func (t *AliyunBalanceTool) Name() string {
	return "get_aliyun_balance"
}

// Description 返回工具描述
func (t *AliyunBalanceTool) Description() string {
	return "获取阿里云账户余额信息。此工具可以查询当前阿里云账户的可用余额，帮助用户了解账户资金状况和进行成本管理。"
}

// Parameters 返回工具接受的参数规范
func (t *AliyunBalanceTool) Parameters() map[string]interfaces.ParameterSpec {
	return map[string]interfaces.ParameterSpec{
		// Aliyun balance query doesn't require any parameters
		// The tool will query the balance for the configured account
	}
}

// Run 执行工具并返回阿里云账户余额信息
func (t *AliyunBalanceTool) Run(ctx context.Context, input string) (string, error) {
	// Parse input parameters (even though we don't expect any for this tool)
	var params map[string]interface{}
	if input != "" {
		if err := json.Unmarshal([]byte(input), &params); err != nil {
			t.logger.Errorf("解析参数失败: %v", err)
			return "", fmt.Errorf("解析参数失败: %w", err)
		}
	}

	balanceData, err := t.getBalanceData(ctx)
	if err != nil {
		t.logger.Errorf("获取阿里云账户余额失败: %v", err)
		return "", err
	}

	return t.formatBalanceResponse(balanceData), nil
}

// Execute 与 Run 相同，但接受参数字符串
func (t *AliyunBalanceTool) Execute(ctx context.Context, args string) (string, error) {
	return t.Run(ctx, args)
}

// balanceResponse 表示余额查询响应
type balanceResponse struct {
	AvailableAmount string `json:"availableAmount"`
	Currency        string `json:"currency"`
	AccountType     string `json:"accountType"`
}

// getBalanceData 获取阿里云账户余额数据
func (t *AliyunBalanceTool) getBalanceData(ctx context.Context) (*balanceResponse, error) {
	// 在实际实现中，应该从安全的配置源获取凭证
	// 这里需要根据实际的配置系统进行调整
	if t.accessKeyId == "" || t.accessKeySecret == "" {
		return nil, fmt.Errorf("阿里云访问凭证未配置，请检查AccessKeyId和AccessKeySecret")
	}

	// 创建阿里云BSS OpenAPI客户端
	client, err := bssopenapi.NewClientWithAccessKey("cn-hangzhou", t.accessKeyId, t.accessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建阿里云客户端失败: %w", err)
	}

	// 创建查询账户余额请求
	request := bssopenapi.CreateQueryAccountBalanceRequest()

	// 执行查询
	response, err := client.QueryAccountBalance(request)
	if err != nil {
		return nil, fmt.Errorf("查询账户余额失败: %w", err)
	}

	// 检查响应是否成功
	if !response.Success {
		return nil, fmt.Errorf("查询账户余额失败: %s (错误代码: %s)", response.Message, response.Code)
	}

	return &balanceResponse{
		AvailableAmount: response.Data.AvailableAmount,
		Currency:        response.Data.Currency,
		AccountType:     "阿里云账户",
	}, nil
}

// formatBalanceResponse 格式化余额响应为字符串
func (t *AliyunBalanceTool) formatBalanceResponse(data *balanceResponse) string {
	result := fmt.Sprintf("阿里云账户余额查询结果:\n")
	result += fmt.Sprintf("账户类型: %s\n", data.AccountType)
	result += fmt.Sprintf("可用余额: %s %s\n", data.AvailableAmount, data.Currency)
	result += fmt.Sprintf("查询时间: %s\n", "当前时间")

	// 添加余额状态提示
	if data.AvailableAmount != "" {
		result += "\n💡 提示: 请及时关注账户余额，确保服务正常运行"
	}

	return result
}
