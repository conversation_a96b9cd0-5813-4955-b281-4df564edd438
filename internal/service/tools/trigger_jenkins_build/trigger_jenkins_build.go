package trigger_jenkins_build

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/config"
	"net/http"
	"time"

	"github.com/bndr/gojenkins"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/zeromicro/go-zero/core/logx"
)

// JenkinsBuildTool 实现了 Tool 接口，提供Jenkins构建触发功能
type JenkinsBuildTool struct {
	ctx    context.Context
	logger logx.Logger
	config *config.JenkinsConfig
}

func New(ctx context.Context, config *config.JenkinsConfig) *JenkinsBuildTool {
	return &JenkinsBuildTool{
		ctx:    ctx,
		config: config,
		logger: logx.WithContext(ctx),
	}
}

// Name 返回工具名称
func (j *JenkinsBuildTool) Name() string {
	return "trigger_jenkins_build"
}

// Description 返回工具描述
func (j *JenkinsBuildTool) Description() string {
	return "触发Jenkins构建任务。此工具可以连接到Jenkins服务器，触发指定的构建任务，并返回构建状态和构建编号信息。"
}

// Parameters 返回工具接受的参数规范
func (j *JenkinsBuildTool) Parameters() map[string]interfaces.ParameterSpec {
	return map[string]interfaces.ParameterSpec{
		"job_name": {
			Type:        "string",
			Description: "要触发的Jenkins任务名称",
			Required:    true,
		},
		"parameters": {
			Type:        "object",
			Description: "构建参数，以JSON对象形式提供，例如：{'VERSION': 'v1', 'SERVICE': 'admin-api'} 如果未指定参数名则只能使用 SERVICE|VERSION这两个参数",
			Required:    false,
		},
	}
}

// Run 执行工具并返回Jenkins构建信息
func (j *JenkinsBuildTool) Run(ctx context.Context, input string) (string, error) {
	var params struct {
		JobName    string                 `json:"job_name"`
		Parameters map[string]interface{} `json:"parameters,omitempty"`
	}

	if err := json.Unmarshal([]byte(input), &params); err != nil {
		j.logger.Errorf("解析参数失败: %v", err)
		return "", fmt.Errorf("解析参数失败: %w", err)
	}

	// 验证必需参数
	if params.JobName == "" {
		return "", fmt.Errorf("缺少必需参数：job_name")
	}

	buildResult, err := j.triggerBuild(ctx, j.config.Url, params.JobName, j.config.Username, j.config.Password, params.Parameters)
	if err != nil {
		j.logger.Errorf("触发Jenkins构建失败: %v", err)
		return "", err
	}

	return j.formatBuildResponse(buildResult), nil
}

// Execute 与 Run 相同，但接受参数字符串
func (j *JenkinsBuildTool) Execute(ctx context.Context, args string) (string, error) {
	return j.Run(ctx, args)
}

// buildResult 表示构建结果
type buildResult struct {
	JobName     string `json:"job_name"`
	BuildNumber int64  `json:"build_number"`
	BuildResult string `json:"build_result"`
	Status      string `json:"status"`
	QueueID     int64  `json:"queue_id"`
	BuildURL    string `json:"build_url"`
}

// triggerBuild 触发Jenkins构建
func (j *JenkinsBuildTool) triggerBuild(ctx context.Context, jenkinsURL, jobName, username, token string, parameters map[string]interface{}) (*buildResult, error) {
	// 创建Jenkins客户端
	jenkins := gojenkins.CreateJenkins(nil, jenkinsURL, username, token)

	// 设置HTTP客户端超时
	jenkins.Requester.Client = &http.Client{
		Timeout: 30 * time.Second,
	}

	// 初始化Jenkins连接
	_, err := jenkins.Init(ctx)
	if err != nil {
		return nil, fmt.Errorf("连接Jenkins服务器失败: %w", err)
	}

	// 获取任务信息验证任务是否存在
	_, err = jenkins.GetJob(ctx, jobName)
	if err != nil {
		return nil, fmt.Errorf("获取Jenkins任务 '%s' 失败: %w", jobName, err)
	}

	// 触发构建
	var queueID int64
	if parameters != nil && len(parameters) > 0 {
		j.logger.Infof("带参数构建任务 '%s',params '%s'", jobName, parameters)
		// 转换参数类型
		stringParams := make(map[string]string)
		for k, v := range parameters {
			stringParams[k] = fmt.Sprintf("%v", v)
		}
		// 带参数构建
		queueID, err = jenkins.BuildJob(ctx, jobName, stringParams)
	} else {
		// 无参数构建
		queueID, err = jenkins.BuildJob(ctx, jobName, map[string]string{})
	}

	if err != nil {
		return nil, fmt.Errorf("触发构建失败: %w", err)
	}

	// 等待构建开始并获取构建编号
	buildNumber, buildResultString, err := j.waitForBuildStart(ctx, jenkins, queueID)
	if err != nil {
		j.logger.Errorf("获取构建编号失败，但构建已触发: %v", err)
		// 即使获取构建编号失败，也返回成功结果
		return &buildResult{
			JobName:     jobName,
			BuildNumber: 0,
			BuildResult: fmt.Sprintf("构建已触发，但获取构建编号失败: %s", err),
			Status:      "已触发",
			QueueID:     queueID,
			BuildURL:    fmt.Sprintf("%s/job/%s/", jenkinsURL, jobName),
		}, nil
	}

	return &buildResult{
		JobName:     jobName,
		BuildNumber: buildNumber,
		BuildResult: buildResultString,
		Status:      "构建已开始",
		QueueID:     queueID,
		BuildURL:    fmt.Sprintf("%s/job/%s/%d/", jenkinsURL, jobName, buildNumber),
	}, nil
}

// waitForBuildStart 等待构建开始并返回构建编号
func (j *JenkinsBuildTool) waitForBuildStart(ctx context.Context, jenkins *gojenkins.Jenkins, queueID int64) (int64, string, error) {
	build, err := jenkins.GetBuildFromQueueID(ctx, queueID)
	if err != nil {
		return 0, "", fmt.Errorf("获取Jenkins任务队列ID '%d' 失败: %w", queueID, err)
	}

	// Wait for build to finish
	for build.IsRunning(ctx) {
		time.Sleep(5000 * time.Millisecond)
		_, err = build.Poll(ctx)
		if err != nil {
			return 0, "", fmt.Errorf("轮询Jenkins任务队列ID '%d' 失败: %w", queueID, err)
		}
	}
	return build.GetBuildNumber(), build.GetResult(), nil
}

// formatBuildResponse 格式化构建响应为字符串
func (j *JenkinsBuildTool) formatBuildResponse(result *buildResult) string {
	response := fmt.Sprintf("Jenkins构建触发成功:\n")
	response += fmt.Sprintf("任务名称: %s\n", result.JobName)
	response += fmt.Sprintf("构建状态: %s\n", result.Status)
	response += fmt.Sprintf("队列ID: %d\n", result.QueueID)

	if result.BuildNumber > 0 {
		response += fmt.Sprintf("构建编号: %d\n", result.BuildNumber)
		response += fmt.Sprintf("构建结果: %s\n", result.BuildResult)
	}
	response += fmt.Sprintf("构建URL: %s\n", result.BuildURL)
	response += fmt.Sprintf("触发时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	response += "\n💡 提示: 请访问构建URL查看详细的构建进度和日志"

	return response
}
