package mastermind

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/pkg/agent"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
	"github.com/run-bigpig/hongdou/internal/pkg/structuredoutput"
	"github.com/run-bigpig/hongdou/internal/provider"
	"github.com/run-bigpig/hongdou/internal/service"
	"github.com/run-bigpig/hongdou/internal/service/engine"
	"github.com/run-bigpig/hongdou/internal/service/message"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/run-bigpig/hongdou/internal/utils"
	"github.com/zeromicro/go-zero/core/logx"
)

type Mastermind struct {
	ctx                context.Context
	svcCtx             *svc.ServiceContext
	logger             logx.Logger
	agent              *agent.Agent
	progressChan       chan<- types.StreamResponse
	shortMemoryMessage *message.Message
	longMemoryMessage  *message.Message
}

func NewMastermind(ctx context.Context, svcCtx *svc.ServiceContext, stream chan<- types.StreamResponse) *Mastermind {
	m := &Mastermind{
		ctx:                ctx,
		svcCtx:             svcCtx,
		logger:             logx.WithContext(ctx),
		progressChan:       stream,
		shortMemoryMessage: message.NewMessage(ctx, svcCtx.RedisMemory),
		longMemoryMessage:  message.NewMessage(ctx, svcCtx.VectorMemory),
	}
	m.loadAgent(svcCtx.Config.MasterMind.LLM, svcCtx.Config.MasterMind.Model)
	return m
}

func (m *Mastermind) loadAgent(llm, model string) {
	a, err := agent.NewAgent(
		agent.WithLLM(provider.NewProvider(m.ctx, m.svcCtx, llm, model)),
		agent.WithSystemPrompt(m.svcCtx.Config.MasterMind.Prompt),
		agent.WithName("HongDouMind"),
		agent.WithResponseFormat(*structuredoutput.NewResponseFormat(service.StructOutput{})),
	)
	if err != nil {
		log.Fatalf("new agent error: %v", err)
	}
	m.agent = a
}

// Run 执行主脑思考规划
func (m *Mastermind) Run(question string) (string, error) {
	// Send progress update if streaming is enabled
	m.sendProgress("🐾 正在搜索工具箱....")

	// Get available tools for context
	availableTools := m.getAvailableToolsContext(question)

	// Send progress update if streaming is enabled
	m.sendProgress("✨ 正在优化系统提示词....")

	// Create enhanced prompt with tool context
	enhancedQuestion, err := m.buildEnhancedPrompt(question, availableTools)
	if err != nil {
		m.sendProgress("⚠️ 提示词整理失败,请检查")
		return "", err
	}
	m.logger.Debugf("=========================Mastermind enhanced prompt: %s", enhancedQuestion)
	// Send progress update if streaming is enabled
	m.sendProgress("🧠 开始规划...")

	// Implement retry logic with response format validation
	var jsonData service.StructOutput
	const maxRetries = 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		response, err := m.agent.Run(m.ctx, enhancedQuestion)
		if err != nil {
			m.logger.Errorf("Attempt %d: Agent run failed: %v", attempt, err)
			if attempt < maxRetries {
				m.sendProgress(fmt.Sprintf("😾 第%d次规划失败", attempt))
				// Brief delay before retry (exponential backoff: 1s, 2s)
				time.Sleep(time.Duration(attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("failed after %d attempts: %w", maxRetries, err)
		}

		m.logger.Debugf("=========================Mastermind response (attempt %d): %s", attempt, response)

		// Try to unmarshal JSON
		err = json.Unmarshal([]byte(response), &jsonData)
		if err != nil {
			m.logger.Errorf("Attempt %d: JSON unmarshal failed: %v, response: %s", attempt, err, response)
			if attempt < maxRetries {
				m.sendProgress(fmt.Sprintf("😿 第%d次规划失败", attempt))
				// Brief delay before retry
				time.Sleep(time.Duration(attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("failed to parse JSON after %d attempts: %w", maxRetries, err)
		}

		// Validate response format
		if validationErr := m.validateStructOutput(&jsonData); validationErr != nil {
			m.logger.Errorf("Attempt %d: Response validation failed: %v, data: %+v", attempt, validationErr, jsonData)
			if attempt < maxRetries {
				m.sendProgress(fmt.Sprintf("🙄 第%d次规划失败", attempt))
				// Brief delay before retry
				time.Sleep(time.Duration(attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("response validation failed after %d attempts: %w", maxRetries, validationErr)
		}

		// Success!
		if attempt > 1 {
			m.sendProgress("🎉 规划完成")
		}
		break
	}

	// Handle different dispatch modes
	switch jsonData.DispatchMode {
	case consts.MasterMindDispatchMode_DIRECT_ANSWER:
		// 记录助手消息
		m.shortMemoryMessage.AddAssistantMessage(jsonData.Answer)
		return jsonData.Answer, nil

	case consts.MasterMindDispatchMode_AGENT_EXECUTION:
		m.sendProgress(fmt.Sprintf("🚀 即将调用%d个智能体解决问题", len(jsonData.SubAgents)))

		// Set the SubQuestion for each agent if not already set
		for i := range jsonData.SubAgents {
			if jsonData.SubAgents[i].SubQuestion == "" {
				jsonData.SubAgents[i].SubQuestion = question
			}
		}
		agentResult, err := engine.NewExecutionEngine(m.ctx, m.svcCtx, m.progressChan).ExecuteMultiAgent(&engine.ExecutionPlan{
			SubAgents: jsonData.SubAgents,
		})
		if err != nil {
			return "", err
		}
		//主脑进行总结
		reviseResult, err := m.revise(jsonData.ReviseSystemPrompt, agentResult)
		if err != nil {
			return "", err
		}
		// 记录助手消息
		m.shortMemoryMessage.AddAssistantMessage(reviseResult)
		return reviseResult, nil
	default:
		return "", errors.New("unknown dispatch mode: " + string(jsonData.DispatchMode))
	}
}

// getAvailableToolsContext returns a context string describing available tools
func (m *Mastermind) getAvailableToolsContext(query string) string {
	// Use semantic tool discovery if available
	if m.svcCtx.Tools != nil {
		return m.svcCtx.Tools.GetToolsContext(query, 10)
	}
	return ""
}

// buildEnhancedPrompt creates a comprehensive user prompt that integrates relevant memories and context
func (m *Mastermind) buildEnhancedPrompt(question, toolsContext string) (string, error) {
	// Record user message first for proper conversation flow
	m.shortMemoryMessage.AddUserMessage(question)

	// Build comprehensive user prompt with enhanced memory integration
	var prompt strings.Builder

	originId, err := multitenancy.GetOrgID(m.ctx)
	if err != nil {
		m.logger.Errorf("Failed to get org id: %v", err)
		return "", err
	}
	userId, err := multitenancy.GetUserID(m.ctx)
	if err != nil {
		m.logger.Errorf("Failed to get user id: %v", err)
		return "", err
	}
	// === USER CONTEXT & IDENTIFICATION ===
	userContext := m.buildUserContextSection(originId, userId)
	if userContext != "" {
		prompt.WriteString("## USER CONTEXT\n")
		prompt.WriteString(userContext)
		prompt.WriteString("\n\n")
	}

	// === RELEVANT MEMORIES ===
	memoryContext := m.buildMemoryContextSection(originId, userId, question)
	if memoryContext != "" {
		prompt.WriteString("## RELEVANT CONTEXT FROM MEMORY\n")
		prompt.WriteString(memoryContext)
		prompt.WriteString("\n\n")
	}

	// === RECENT CONVERSATION ===
	conversationContext := m.buildConversationContextSection()
	if conversationContext != "" {
		prompt.WriteString("## RECENT CONVERSATION HISTORY\n")
		prompt.WriteString(conversationContext)
		prompt.WriteString("\n\n")
	}

	// === AVAILABLE CAPABILITIES ===
	if toolsContext != "" {
		prompt.WriteString("## AVAILABLE TOOLS & CAPABILITIES\n")
		prompt.WriteString(toolsContext)
		prompt.WriteString("\n\n")
	}

	// === CURRENT TASK ===
	prompt.WriteString("## CURRENT USER QUERY\n")
	prompt.WriteString(fmt.Sprintf("**Query**: %s\n\n", question))

	return prompt.String(), nil
}

// buildUserContextSection creates user identification and context information
func (m *Mastermind) buildUserContextSection(originId, userId string) string {
	var ctx strings.Builder
	if originId != "" {
		ctx.WriteString(fmt.Sprintf("**Organization**: %s\n", originId))
		if userId != "" {
			ctx.WriteString(fmt.Sprintf("**User**: %s (Organization: %s)\n", userId, originId))
		}
	}
	return ctx.String()
}

// buildMemoryContextSection retrieves and formats relevant memories with enhanced user isolation
func (m *Mastermind) buildMemoryContextSection(originId, userId, question string) string {
	var ctx strings.Builder

	// Retrieve user-specific long-term memories with enhanced filtering
	personalMemories := m.longMemoryMessage.GetMessages(
		interfaces.WithQuery(question),
		interfaces.WithLimit(8), // Increased limit for richer ctx
		interfaces.WithVectorStoreSearchOptions(interfaces.WithFilters(map[string]interface{}{"user_id": userId, "origin_id": originId})),
	)

	// Retrieve system-level memories (instructions, preferences, etc.)
	knowledgeMemories := m.longMemoryMessage.GetMessages(
		interfaces.WithQuery(question),
		interfaces.WithLimit(5),
		interfaces.WithVectorStoreSearchOptions(interfaces.WithFilters(map[string]interface{}{"role": "knowledge"})),
	)

	// Add knowledge ctx if available
	if len(knowledgeMemories) > 0 {
		ctx.WriteString("### Knowledge Instructions & Guidelines\n")
		for i, memory := range knowledgeMemories {
			ctx.WriteString(fmt.Sprintf("%d. %s\n", i+1, memory.Content))
		}
		ctx.WriteString("\n")
	}

	// Add personal memories if available
	if len(personalMemories) > 0 {
		ctx.WriteString("### Personal Context & Preferences\n")
		for i, memory := range personalMemories {
			// Include role information for better ctx
			roleIndicator := ""
			if memory.Role != "" {
				roleIndicator = fmt.Sprintf("[%s] ", memory.Role)
			}
			ctx.WriteString(fmt.Sprintf("%d. %s%s\n", i+1, roleIndicator, memory.Content))
		}
		ctx.WriteString("\n")
	}

	// Add relevance note if memories were found
	if len(knowledgeMemories) > 0 || len(personalMemories) > 0 {
		ctx.WriteString("*Note: Above ctx retrieved based on semantic relevance to your query.*\n")
	}

	return ctx.String()
}

// buildConversationContextSection formats recent conversation history
func (m *Mastermind) buildConversationContextSection() string {
	// Get recent conversation history (excluding the just-added user message)
	historyMessages := m.shortMemoryMessage.GetMessages(interfaces.WithLimit(8))

	if len(historyMessages) <= 1 { // Only the current message or empty
		return ""
	}

	var ctx strings.Builder
	ctx.WriteString("```\n")

	// Format conversation history (exclude the last message which is the current query)
	for i, msg := range historyMessages {
		if i == len(historyMessages)-1 {
			break // Skip the current message we just added
		}
		ctx.WriteString(fmt.Sprintf("%s: %s\n", msg.Role, msg.Content))
	}

	ctx.WriteString("```\n")
	return ctx.String()
}

func (m *Mastermind) sendProgress(message string) {
	utils.SendProgress(m.progressChan, message)
}

// validateStructOutput validates the parsed JSON response to ensure all required fields are present
func (m *Mastermind) validateStructOutput(data *service.StructOutput) error {
	// Validate DispatchMode is present and valid
	if data.DispatchMode == "" {
		return fmt.Errorf("missing required field: dispatch_mode")
	}

	// Validate based on dispatch mode
	switch data.DispatchMode {
	case consts.MasterMindDispatchMode_DIRECT_ANSWER:
		if data.Answer == "" {
			return fmt.Errorf("DIRECT_ANSWER mode requires non-empty 'answer' field")
		}

	case consts.MasterMindDispatchMode_AGENT_EXECUTION:
		if len(data.SubAgents) == 0 {
			return fmt.Errorf("AGENT_EXECUTION mode requires non-empty 'sub_agents' array")
		}

		// Validate each sub-agent configuration
		for i, sa := range data.SubAgents {
			if sa.AgentName == "" {
				return fmt.Errorf("sub_agents[%d] missing required field: agent_name", i)
			}
			if sa.SystemPrompt == "" {
				return fmt.Errorf("sub_agents[%d] missing required field: system_prompt", i)
			}
			// enabled_tools can be empty array, but should not be nil
			if sa.EnabledTools == nil {
				return fmt.Errorf("sub_agents[%d] missing required field: enabled_tools (can be empty array)", i)
			}
		}

	default:
		return fmt.Errorf("invalid dispatch_mode: %s (must be DIRECT_ANSWER or AGENT_EXECUTION)", data.DispatchMode)
	}

	return nil
}

// revise synthesizes multi-agent results into a final response
func (m *Mastermind) revise(reviseSystemPrompt, input string) (string, error) {
	// Update the mastermind agent's system prompt for synthesis
	m.agent.ModifyPrompt(fmt.Sprintf("%s\n%s", reviseSystemPrompt, "Finally, please reply in Chinese and use markdown format as much as possible"))
	// unset response format
	m.agent.ModifyResponseFormat(nil)
	// Send progress update
	m.sendProgress("开始总结")

	// Build comprehensive synthesis prompt with user context and agent results
	synthesisPrompt := m.buildRevisePrompt(input)

	// Process through LLM for final synthesis
	response, err := m.agent.Run(m.ctx, synthesisPrompt)
	if err != nil {
		return "", fmt.Errorf("failed to synthesize agent results: %w", err)
	}
	// Send completion progress
	m.sendProgress("总结完成")

	return response, nil
}

// buildSynthesisPrompt creates a comprehensive prompt for synthesizing multi-agent results
func (m *Mastermind) buildRevisePrompt(agentResults string) string {
	var prompt strings.Builder
	// === AGENT RESULTS TO SYNTHESIZE ===
	prompt.WriteString("## AGENT ANALYSIS RESULTS\n")
	prompt.WriteString("The following results were generated by specialized agents analyzing different aspects of the user's query:\n\n")
	prompt.WriteString(agentResults)
	prompt.WriteString("\n\n")
	return prompt.String()
}
