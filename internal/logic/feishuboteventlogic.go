package logic

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/consts"

	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type FeiShuBotEventLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 飞书机器人事件
func NewFeiShuBotEventLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FeiShuBotEventLogic {
	return &FeiShuBotEventLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FeiShuBotEventLogic) FeiShuBotEvent(req *types.FeiShuBotReceiveEventRequest) (resp *types.Empty, err error) {
	if req.Header.Token != l.svcCtx.Config.BotConfig.FeishuBot.EventToken {
		return nil, errors.New("invalid token")
	}
	eventMap := map[string]any{
		"type": req.Header.EventType,
		"data": req.Event,
	}
	eventBytes, err := json.Marshal(eventMap)
	if err != nil {
		return nil, err
	}
	botEvent := bot.BotEvent{
		Bot:   "feishu",
		Event: eventBytes,
	}
	botEventBytes, err := json.Marshal(botEvent)
	if err != nil {
		return nil, err
	}
	err = l.svcCtx.Pusher.Publish(consts.KgBotEventTopic, botEventBytes)
	return
}
