package svc

import (
	"context"
	"github.com/go-redis/redis/v8"
	"github.com/nsqio/go-nsq"
	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/pkg/embedding"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/memory"
	"github.com/run-bigpig/hongdou/internal/pkg/mq"
	"github.com/run-bigpig/hongdou/internal/pkg/vectorstore/weaviate"
	"github.com/run-bigpig/hongdou/internal/service/tools"
	"time"
)

type ServiceContext struct {
	Config       config.Config
	VectorStore  *weaviate.Store
	RedisMemory  interfaces.Memory
	VectorMemory interfaces.Memory
	Tools        *tools.Tools
	Pusher       *nsq.Producer
}

func NewServiceContext(ctx context.Context, c config.Config) *ServiceContext {
	svcCtx := &ServiceContext{
		Config: c,
	}
	initRuntime(ctx, svcCtx)
	return svcCtx
}

func initRuntime(ctx context.Context, svcCtx *ServiceContext) {
	//初始化Embedder
	embeddingConfig := embedding.DefaultEmbeddingConfig(svcCtx.Config.EmbedderConfig.Model)
	embeddingConfig.Dimensions = svcCtx.Config.EmbedderConfig.Dimension
	embeddingConfig.SimilarityMetric = svcCtx.Config.EmbedderConfig.SimilarityMetric
	embeddingConfig.SimilarityThreshold = svcCtx.Config.EmbedderConfig.SimilarityThreshold
	embedder := embedding.NewOpenAIEmbedderWithConfig(svcCtx.Config.EmbedderConfig.BaseUrl, svcCtx.Config.EmbedderConfig.Sk, embeddingConfig)
	//初始化VectorStore
	svcCtx.VectorStore = weaviate.New(&interfaces.VectorStoreConfig{
		Host:   svcCtx.Config.VectorStore.Weaviate.Host,
		Scheme: svcCtx.Config.VectorStore.Weaviate.Schema,
		APIKey: svcCtx.Config.VectorStore.Weaviate.ApiKey,
	},
		weaviate.WithClassPrefix(consts.VectorStorePrefix),
		weaviate.WithEmbedder(embedder))
	//初始化RedisMemory
	svcCtx.RedisMemory = memory.NewRedisMemory(redis.NewClient(&redis.Options{
		Addr:     svcCtx.Config.RedisConfig.Addr,
		Password: svcCtx.Config.RedisConfig.Pass,
	}), memory.WithKeyPrefix(consts.MemoryPrefix), memory.WithTTL(10*time.Minute))
	//初始化VectorMemory
	svcCtx.VectorMemory = memory.NewVectorStoreMemory(svcCtx.VectorStore)
	//初始化Tools
	svcCtx.Tools = tools.NewTools(ctx, svcCtx.Config.McpServerConfig, svcCtx.Config.SelfToolsConfig)
	//初始化Tool Discovery (after embedder and vector store are available)
	svcCtx.Tools.SetDiscovery(embedder, svcCtx.VectorStore)
	//初始化消息生产者
	svcCtx.Pusher = mq.InitProducer(svcCtx.Config.NsqConfig.Nsqd)
}
