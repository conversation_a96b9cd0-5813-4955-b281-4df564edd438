// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package types

type ChatRequest struct {
	ConversationId string `json:"conversation_id"`
	Message        string `json:"message"`
	Stream         bool   `json:"stream,optional"`
}

type ChatResponse struct {
	Message string `json:"message"`
}

type Empty struct {
}

type FeiShuBotEventHeader struct {
	EventId    string `json:"event_id"`    //事件ID
	EventType  string `json:"event_type"`  //事件类型
	CreateTime string `json:"create_time"` //事件创建时间戳 单位毫秒
	Token      string `json:"token"`       //事件token
	AppId      string `json:"app_id"`      //应用ID
	TenantKey  string `json:"tenant_key"`  //租户Key
}

type FeiShuBotReceiveEventRequest struct {
	Schema string                 `json:"schema"` //事件模式
	Header FeiShuBotEventHeader   `json:"header"` //事件头
	Event  map[string]interface{} `json:"event"`  //事件体
}

type SaveMemoryQuest struct {
	Content string `json:"content"`
}

type StreamResponse struct {
	Type    string `json:"type"` // "progress", "result", "error"
	Content string `json:"content"`
	Done    bool   `json:"done"`
}
