package provider

import (
	"context"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/llm/openai"
	"github.com/run-bigpig/hongdou/internal/pkg/llm/vertex"
	"github.com/run-bigpig/hongdou/internal/pkg/retry"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/zeromicro/go-zero/core/logx"
	"log"
)

type Provider struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logger logx.Logger
	interfaces.LLM
}

func NewProvider(ctx context.Context, svcCtx *svc.ServiceContext, name string, model string) *Provider {
	provider := &Provider{
		ctx:    ctx,
		svcCtx: svcCtx,
		logger: logx.WithContext(ctx),
	}
	provider.setLLM(name, model)
	if provider.LLM == nil {
		log.Fatalln("unknown llm provider")
	}
	return provider
}

func (p *Provider) setLLM(name, model string) {
	switch name {
	case "openai":
		p.LLM = openai.NewClient(p.svcCtx.Config.LLMConfig.OpenAi.BaseUrl, p.svcCtx.Config.LLMConfig.OpenAi.Sk, openai.WithModel(model), openai.WithRetry(retry.WithMaximumInterval(3)))
	case "vertex":
		client, err := vertex.NewClient(p.ctx, p.svcCtx.Config.LLMConfig.Vertex.Project, vertex.WithModel(model), vertex.WithCredentialsJSON([]byte(p.svcCtx.Config.LLMConfig.Vertex.ApiKey)))
		if err != nil {
			p.logger.Error("failed to create vertex client", err)
		}
		p.LLM = client
	default:
		p.logger.Error("unknown llm provider")
	}
}
