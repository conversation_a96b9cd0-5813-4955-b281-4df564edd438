package bot

import (
	"context"
	"fmt"
	"sync"

	"github.com/zeromicro/go-zero/core/logx"
)

// DefaultBotManager 默认机器人管理器实现
type DefaultBotManager struct {
	bots   map[BotPlatform]Bot
	mutex  sync.RWMutex
	logger logx.Logger
}

// NewBotManager 创建新的机器人管理器
func NewBotManager() BotManager {
	return &DefaultBotManager{
		bots:   make(map[BotPlatform]Bot),
		logger: logx.WithContext(context.Background()),
	}
}

// RegisterBot 注册机器人
func (m *DefaultBotManager) RegisterBot(platform BotPlatform, bot Bot) error {
	if bot == nil {
		return fmt.Errorf("机器人实例不能为空")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.bots[platform]; exists {
		return fmt.Errorf("平台 %s 的机器人已经注册", platform)
	}

	m.bots[platform] = bot
	m.logger.Infof("成功注册 %s 平台机器人", platform)
	return nil
}

// GetBot 获取指定平台的机器人
func (m *DefaultBotManager) GetBot(platform BotPlatform) (Bot, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	bot, exists := m.bots[platform]
	if !exists {
		return nil, fmt.Errorf("平台 %s 的机器人未注册", platform)
	}

	return bot, nil
}

// GetAllBots 获取所有注册的机器人
func (m *DefaultBotManager) GetAllBots() map[BotPlatform]Bot {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[BotPlatform]Bot)
	for platform, bot := range m.bots {
		result[platform] = bot
	}

	return result
}

// StartAll 启动所有机器人
func (m *DefaultBotManager) StartAll(ctx context.Context) error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var errors []error
	for platform, bot := range m.bots {
		if err := bot.Initialize(ctx); err != nil {
			errorMsg := fmt.Errorf("启动 %s 平台机器人失败: %w", platform, err)
			errors = append(errors, errorMsg)
			m.logger.Error(errorMsg.Error())
		} else {
			m.logger.Infof("成功启动 %s 平台机器人", platform)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("启动机器人时发生 %d 个错误", len(errors))
	}

	return nil
}

// StopAll 停止所有机器人
func (m *DefaultBotManager) StopAll() error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var errors []error
	for platform, bot := range m.bots {
		if err := bot.Cleanup(); err != nil {
			errorMsg := fmt.Errorf("停止 %s 平台机器人失败: %w", platform, err)
			errors = append(errors, errorMsg)
			m.logger.Error(errorMsg.Error())
		} else {
			m.logger.Infof("成功停止 %s 平台机器人", platform)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("停止机器人时发生 %d 个错误", len(errors))
	}

	return nil
}

// DefaultEventHandler 默认事件处理器实现
type DefaultEventHandler struct {
	logger logx.Logger
}

// NewEventHandler 创建新的事件处理器
func NewEventHandler() EventHandler {
	return &DefaultEventHandler{
		logger: logx.WithContext(context.Background()),
	}
}

// HandleMessage 处理消息事件
func (h *DefaultEventHandler) HandleMessage(ctx context.Context, event *Event) error {
	h.logger.Infof("处理消息事件: %s", event.Type)
	// 这里可以添加具体的消息处理逻辑
	return nil
}

// HandleJoinChat 处理加入聊天事件
func (h *DefaultEventHandler) HandleJoinChat(ctx context.Context, event *Event) error {
	h.logger.Infof("处理加入聊天事件: %s", event.Type)
	// 这里可以添加具体的加入聊天处理逻辑
	return nil
}

// HandleLeaveChat 处理离开聊天事件
func (h *DefaultEventHandler) HandleLeaveChat(ctx context.Context, event *Event) error {
	h.logger.Infof("处理离开聊天事件: %s", event.Type)
	// 这里可以添加具体的离开聊天处理逻辑
	return nil
}

// HandleUnknownEvent 处理未知事件
func (h *DefaultEventHandler) HandleUnknownEvent(ctx context.Context, event *Event) error {
	h.logger.Errorf("处理未知事件: %s", event.Type)
	// 这里可以添加具体的未知事件处理逻辑
	return nil
}

// DefaultMessageProcessor 默认消息处理器实现
type DefaultMessageProcessor struct {
	logger logx.Logger
}

// NewMessageProcessor 创建新的消息处理器
func NewMessageProcessor() MessageProcessor {
	return &DefaultMessageProcessor{
		logger: logx.WithContext(context.Background()),
	}
}

// ProcessTextMessage 处理文本消息
func (p *DefaultMessageProcessor) ProcessTextMessage(ctx context.Context, message *ImMessageReceiveEvent) error {
	p.logger.Infof("处理文本消息: %s", message.Message.MessageID)
	// 这里可以添加具体的文本消息处理逻辑
	return nil
}

// ProcessFileMessage 处理文件消息
func (p *DefaultMessageProcessor) ProcessFileMessage(ctx context.Context, message *ImMessageReceiveEvent) error {
	p.logger.Infof("处理文件消息: %s", message.Message.MessageID)
	// 这里可以添加具体的文件消息处理逻辑
	return nil
}

// ProcessImageMessage 处理图片消息
func (p *DefaultMessageProcessor) ProcessImageMessage(ctx context.Context, message *ImMessageReceiveEvent) error {
	p.logger.Infof("处理图片消息: %s", message.Message.MessageID)
	// 这里可以添加具体的图片消息处理逻辑
	return nil
}

// BotService 机器人服务，提供统一的机器人操作接口
type BotService struct {
	manager   BotManager
	handler   EventHandler
	processor MessageProcessor
	logger    logx.Logger
}

// NewBotService 创建新的机器人服务
func NewBotService(manager BotManager, handler EventHandler, processor MessageProcessor) *BotService {
	return &BotService{
		manager:   manager,
		handler:   handler,
		processor: processor,
		logger:    logx.WithContext(context.Background()),
	}
}

// HandleEvent 处理事件，自动路由到对应的机器人
func (s *BotService) HandleEvent(ctx context.Context, event *Event) error {
	bot, err := s.manager.GetBot(event.Platform)
	if err != nil {
		return fmt.Errorf("获取 %s 平台机器人失败: %w", event.Platform, err)
	}

	return bot.HandleEvent(ctx, event)
}

// SendMessage 发送消息到指定平台
func (s *BotService) SendMessage(ctx context.Context, platform BotPlatform, chatType, chatId, text string) error {
	bot, err := s.manager.GetBot(platform)
	if err != nil {
		return fmt.Errorf("获取 %s 平台机器人失败: %w", platform, err)
	}

	return bot.SendTextMessage(ctx, chatType, chatId, text)
}

// GetBotStatus 获取所有机器人的健康状态
func (s *BotService) GetBotStatus() map[BotPlatform]bool {
	bots := s.manager.GetAllBots()
	status := make(map[BotPlatform]bool)

	for platform, bot := range bots {
		status[platform] = bot.IsHealthy()
	}

	return status
}
