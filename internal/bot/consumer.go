package bot

import (
	"context"
	"encoding/json"
	"github.com/nsqio/go-nsq"
	"github.com/run-bigpig/hongdou/internal/bot/feishu"
	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/pkg/mq"
	"github.com/run-bigpig/hongdou/internal/svc"
)

type BotEvent struct {
	Bot   string
	Event []byte
}
type ConsumeBotMessageHandler struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func Init(ctx context.Context, svcCtx *svc.ServiceContext) {
	mq.InitConsumer(svcCtx.Config.NsqConfig.Lookupd, consts.KgBotEventTopic, 3, &ConsumeBotMessageHandler{ctx: ctx, svcCtx: svcCtx})
}

func (c *ConsumeBotMessageHandler) HandleMessage(msg *nsq.Message) error {
	var botEvent BotEvent
	err := json.Unmarshal(msg.Body, &botEvent)
	if err != nil {
		msg.Finish()
		return err
	}
	switch botEvent.Bot {
	case "feishu":
		var feishuEvent feishu.Event
		err = json.Unmarshal(botEvent.Event, &feishuEvent)
		if err != nil {
			msg.Finish()
			return err
		}
		return feishu.NewFeishuBot(c.ctx, c.svcCtx).HandleEvent(&feishuEvent)
	default:
		return nil
	}
}
