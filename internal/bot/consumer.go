package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"game/common/mq"
	"game/common/utils"
	"game/rpc/bot/internal/consts"
	"game/rpc/bot/internal/directive"
	"game/rpc/bot/internal/svc"
	"game/rpc/bot/internal/types"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
	"github.com/nsqio/go-nsq"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"math/rand"
	url2 "net/url"
	"regexp"
	"sort"
	"strings"
)

var (
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logger logx.Logger
)

func Init(c context.Context, s *svc.ServiceContext) {
	ctx = c
	svcCtx = s
	logger = logx.WithContext(ctx)
	mq.InitConsumer(svcCtx.Config.NsqConf.Lookupd, consts.KgBotEventTopic, 3, &ConsumeEventHandler{})
}

type ConsumeEventHandler struct{}

func (c *ConsumeEventHandler) HandleMessage(msg *nsq.Message) error {
	var event types.Event
	if err := json.Unmarshal(msg.Body, &event); err != nil {
		logger.Errorf("Failed to unmarshal event: %v", err)
		msg.Finish()
		return err
	}

	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		logger.Errorf("Failed to marshal event data: %v", err)
		msg.Finish()
		return err
	}

	switch event.Type {
	case consts.EventTypeImMessageReceive:
		if err := c.handleImMessage(dataBytes); err != nil {
			logger.Errorf("Failed to handle IM message: %v", err)
			msg.Finish()
			return err
		}
	case consts.EventP2PJoinChat:
		if err := c.handleP2PJoinChat(dataBytes); err != nil {
			logger.Errorf("Failed to handle P2P join chat: %v", err)
			msg.Finish()
			return err
		}
	default:
		logger.Errorf("Unknown event type: %s", event.Type)
		msg.Finish()
		return nil
	}
	return nil
}

func (c *ConsumeEventHandler) handleP2PJoinChat(data []byte) error {
	var message types.P2PJoinChat
	if err := json.Unmarshal(data, &message); err != nil {
		logger.Errorf("Failed to unmarshal P2P join chat: %v", err)
		return err
	}
	//
	//if err := SendTextMessage("p2p", message.OperatorId.OpenId, "您好，我是智能助手，有什么可以帮您的吗？"); err != nil {
	//	logger.Errorf("Failed to send welcome message: %v", err)
	//	return err
	//}
	return nil
}

func (c *ConsumeEventHandler) handleImMessage(data []byte) error {
	var message types.ImMessageReceiveEvent
	if err := json.Unmarshal(data, &message); err != nil {
		logger.Errorf("Failed to unmarshal IM message: %v", err)
		return err
	}
	//判断当前消息是否@了机器人
	if message.Message.ChatType == "group" {
		var bot bool
		for _, v := range message.Message.Mentions {
			if v.Name == "鲲游服务机器人" {
				bot = true
			}
		}
		if !bot {
			return nil
		}
	}
	switch message.Message.MessageType {
	case "text":
		return c.handleTextMessage(message)
	default:
		logger.Errorf("Unsupported message type: %s", message.Message.MessageType)
		return errors.New("Unsupported message type")
	}
}

func (c *ConsumeEventHandler) handleTextMessage(message types.ImMessageReceiveEvent) error {
	if message.Message.Content == "" {
		return errors.New("message content is empty")
	}
	var content struct {
		Text string `json:"text"`
	}
	if err := json.Unmarshal([]byte(message.Message.Content), &content); err != nil {
		logger.Errorf("Failed to unmarshal message content: %v", err)
		return errors.New("Failed to unmarshal message content")
	}
	//表情回复收到处理请求
	emjois := []string{"SaluteFace", "OK", "Get", "OneSecond"}
	//随机取一个表情包发送
	err := ReplyEmojiMessage(message.Message.MessageID, emjois[rand.Intn(len(emjois))])
	if err != nil {
		logger.Errorf("Failed to reply emoji message: %v", err)
	}
	//处理指令
	text := dealText(content.Text)
	if strings.Contains(text, "/cmd ") {
		return c.processDirective(message, text)
	}
	//接入工作流处理
	return c.processWorkflow(message, text)
}

func (c *ConsumeEventHandler) processWorkflow(message types.ImMessageReceiveEvent, text string) error {
	var historyMessageStr string
	files := make([]*types.FileData, 0)
	//如果有回复消息则提取回复消息内容,否则获取历史消息
	if message.Message.ParentID != "" {
		file, txt, err := getMessage(message.Message.ParentID)
		if err != nil {
			return err
		}
		if file != nil {
			files = append(files, file)
		}
		if txt != "" {
			historyMessageStr = txt
		}
	} else {
		//获取历史消息
		historyMessage, err := getHistoryMessage(message.Message.MessageID, message.Message.CreateTime, message.Message.ChatID)
		if err == nil {
			//添加历史文件内容
			files = append(files, historyMessage.Files...)
			//添加历史文本内容
			if len(historyMessage.Texts) > 0 {
				for _, t := range historyMessage.Texts {
					historyMessageStr = fmt.Sprintf("%s\n%s", historyMessageStr, t.Text)
				}
			}
		}
	}
	//解析是否包含飞书知识库地址
	wikiUrl := isFeiShuWiki(text)
	if wikiUrl != "" {
		fileData, err := getFeiShuWikiContent(wikiUrl)
		if err == nil {
			files = append(files, fileData)
		}
	}
	// 移除重复的文件
	existFile := make(map[string]bool)
	newFiles := make([]*types.FileData, 0)
	for _, file := range files {
		if existFile[file.FileKey] {
			continue
		}
		newFiles = append(newFiles, file)
		existFile[file.FileKey] = true
	}
	var resp types.WorkFlowRes
	data := map[string]any{
		"historyMessage": strings.Trim(historyMessageStr, "\n"),
		"directive":      text,
		"messageId":      message.Message.MessageID,
		"userId":         message.Sender.SenderID.OpenID,
		"files":          newFiles,
	}
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	header := map[string]string{
		"Authorization": svcCtx.Config.AiConf.Authorization,
		"Content-Type":  "application/json",
	}
	return utils.SendWithContext(ctx, "POST", svcCtx.Config.AiConf.WorkflowUrl, header, dataBytes, &resp)
}

func (c *ConsumeEventHandler) processDirective(message types.ImMessageReceiveEvent, text string) error {
	directiveProcessor := directive.NewCommandProcessor(ctx, svcCtx)
	resp, err := directiveProcessor.Process(text)
	if err != nil {
		logger.Errorf("Failed to process directive: %v", err)
		return err
	}

	dataBytes, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		logger.Errorf("Failed to marshal directive response: %v", err)
		return err
	}

	res := fmt.Sprintf("```JSON\n%s\n```", string(dataBytes))
	return ReplyInteractiveMessage(message.Message.MessageID, types.CardData{
		TemplateID:          svcCtx.Config.FeishuConf.DirectiveCard.TemplateId,
		TemplateVersionName: svcCtx.Config.FeishuConf.DirectiveCard.TemplateVersion,
		TemplateVariable: map[string]any{
			"result": res,
		},
	})
}

// ReplyInteractiveMessage 回复卡片消息
func ReplyInteractiveMessage(messageId string, cardData types.CardData) error {
	client := lark.NewClient(svcCtx.Config.FeishuConf.AppId, svcCtx.Config.FeishuConf.AppSecret)
	replyContent := types.Card{
		Type: "template",
		Data: cardData,
	}

	replyContentBytes, err := json.Marshal(replyContent)
	if err != nil {
		return fmt.Errorf("failed to marshal reply content: %w", err)
	}

	req := larkim.NewReplyMessageReqBuilder().
		MessageId(messageId).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			Content(string(replyContentBytes)).
			MsgType("interactive").
			ReplyInThread(false).
			Build()).
		Build()

	resp, err := client.Im.V1.Message.Reply(context.Background(), req)
	if err != nil {
		return fmt.Errorf("failed to send reply: %w", err)
	}

	if !resp.Success() {
		return fmt.Errorf("server error - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	return nil
}

// 表情回复
func ReplyEmojiMessage(messageId string, emoji string) error {
	client := lark.NewClient(svcCtx.Config.FeishuConf.AppId, svcCtx.Config.FeishuConf.AppSecret)
	req := larkim.NewCreateMessageReactionReqBuilder().
		MessageId(messageId).
		Body(larkim.NewCreateMessageReactionReqBodyBuilder().
			ReactionType(larkim.NewEmojiBuilder().
				EmojiType(emoji).
				Build()).
			Build()).
		Build()

	resp, err := client.Im.V1.MessageReaction.Create(context.Background(), req)
	if err != nil {
		fmt.Println(err)
		return err
	}
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return errors.New("发送失败")
	}
	return nil
}

// SendTextMessage 发送文本消息
func SendTextMessage(chatType, chatId, text string) error {
	idType := "open_id"
	if chatType == "group" {
		idType = "chat_id"
	}
	client := lark.NewClient(svcCtx.Config.FeishuConf.AppId, svcCtx.Config.FeishuConf.AppSecret)
	var content struct {
		Text string `json:"text"`
	}
	content.Text = text
	textBytes, err := json.Marshal(content)
	if err != nil {
		return err
	}
	req := larkim.NewCreateMessageReqBuilder().ReceiveIdType(idType).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(chatId).
			MsgType("text").
			Content(string(textBytes)).
			Build()).
		Build()

	resp, err := client.Im.V1.Message.Create(context.Background(), req)
	if err != nil {
		return fmt.Errorf("failed to send text message: %w", err)
	}

	if !resp.Success() {
		return fmt.Errorf("server error - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	return nil
}

// 获取历史消息
func getHistoryMessage(nowMessageId, startTime string, chatId string) (*types.HistoryMessage, error) {
	client := lark.NewClient(svcCtx.Config.FeishuConf.AppId, svcCtx.Config.FeishuConf.AppSecret)
	secondEndTime := cast.ToInt64(startTime) / 1000
	secondStartTime := secondEndTime - 10*60 //获取10分钟内的消息
	req := larkim.NewListMessageReqBuilder().
		ContainerIdType("chat").
		ContainerId(chatId).
		StartTime(cast.ToString(secondStartTime)).
		EndTime(cast.ToString(secondEndTime)).
		SortType(`ByCreateTimeDesc`).
		PageSize(5).
		Build()
	// 发起请求
	resp, err := client.Im.V1.Message.List(context.Background(), req)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return nil, errors.New("获取历史消息失败")
	}
	// 处理返回结果
	if resp.Data == nil {
		return nil, errors.New("没有获取到历史消息")
	}
	//将消息按照创建时间升序排列
	sort.Slice(resp.Data.Items, func(i, j int) bool {
		return cast.ToInt64(resp.Data.Items[i].CreateTime) < cast.ToInt64(resp.Data.Items[j].CreateTime)
	})
	// 提取历史消息
	var texts = make([]*types.TextData, 0)
	var files = make([]*types.FileData, 0)
	for _, v := range resp.Data.Items {
		//跳过当前消息
		if *v.MessageId == nowMessageId {
			continue
		}
		if *(v.Sender.SenderType) == "user" || *(v.Sender.SenderType) == "app" {
			switch *v.MsgType {
			case "file":
				var file types.FileData
				err = json.Unmarshal([]byte(*v.Body.Content), &file)
				if err != nil {
					return nil, err
				}
				file.MessageId = *v.MessageId
				files = append(files, &file)
			case "text":
				var text types.TextData
				err = json.Unmarshal([]byte(*v.Body.Content), &text)
				if err != nil {
					return nil, err
				}
				wikiUrl := isFeiShuWiki(text.Text)
				if wikiUrl != "" {
					file, err := getFeiShuWikiContent(wikiUrl)
					if err != nil {
						return nil, err
					}
					file.MessageId = *v.MessageId
					files = append(files, file)
				} else {
					text.MessageId = *v.MessageId
					texts = append(texts, &text)
				}
			}
		}
	}
	return &types.HistoryMessage{
		Files: files,
		Texts: texts,
	}, nil
}

// 获取指定消息内容
func getMessage(messageId string) (*types.FileData, string, error) {
	client := lark.NewClient(svcCtx.Config.FeishuConf.AppId, svcCtx.Config.FeishuConf.AppSecret)
	req := larkim.NewGetMessageReqBuilder().
		MessageId(messageId).
		UserIdType(`open_id`).
		Build()
	resp, err := client.Im.V1.Message.Get(context.Background(), req)
	if err != nil {
		fmt.Printf("request failed: %v\n", err)
		return nil, "", err
	}
	if !resp.Success() {
		fmt.Println("request failed:", resp.Code, resp.Msg, resp.RequestId())
		return nil, "", fmt.Errorf("request failed: %s", resp.Msg)
	}
	message := resp.Data.Items[0]
	switch *message.MsgType {
	case "text":
		var text types.TextData
		if err := json.Unmarshal([]byte(*message.Body.Content), &text); err != nil {
			return nil, "", err
		}
		wikiUrl := isFeiShuWiki(text.Text)
		if wikiUrl != "" {
			file, err := getFeiShuWikiContent(wikiUrl)
			if err != nil {
				return nil, "", err
			}
			file.MessageId = messageId
			return file, "", nil
		}
		return nil, text.Text, nil
	case "file":
		var file types.FileData
		if err := json.Unmarshal([]byte(*message.Body.Content), &file); err != nil {
			return nil, "", err
		}
		file.MessageId = messageId
		return &file, "", nil
	case "post":
		return nil, *message.Body.Content, nil
	default:
		return nil, "", nil
	}
}

// 移除消息中@用户的部分
func dealText(text string) string {
	re := regexp.MustCompile(`@_user_[a-zA-Z0-9-]+`)
	text = re.ReplaceAllString(text, "")
	return strings.TrimSpace(text)
}

// 判断是否包含飞书云知识库内容并提取url地址
func isFeiShuWiki(text string) string {
	if !strings.Contains(text, "https://boke.feishu.cn/wiki/") {
		return ""
	}
	//从字符串中提取url地址
	re := regexp.MustCompile(`https://boke.feishu.cn/wiki/[a-zA-Z0-9-]+`)
	url := re.FindString(text)
	return url
}

// 获取wiki内容
func getFeiShuWikiContent(url string) (*types.FileData, error) {
	client := lark.NewClient(svcCtx.Config.FeishuConf.AppId, svcCtx.Config.FeishuConf.AppSecret)
	u, err := url2.Parse(url)
	if err != nil {
		return nil, err
	}
	pathArr := strings.Split(strings.Trim(u.Path, "/"), "/")
	if len(pathArr) != 2 {
		return nil, errors.New("wiki url error")
	}
	// 创建请求对象
	req := larkwiki.NewGetNodeSpaceReqBuilder().
		Token(pathArr[1]).
		Build()
	// 发起请求
	resp, err := client.Wiki.V2.Space.GetNode(context.Background(), req)
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		fmt.Println("request failed:", resp.Code, resp.Msg, resp.RequestId())
		return nil, fmt.Errorf("request failed: %s", resp.Msg)
	}
	return &types.FileData{
		WikiType: *resp.Data.Node.ObjType,
		FileKey:  *resp.Data.Node.ObjToken,
		FileName: *resp.Data.Node.Title,
	}, nil
}
