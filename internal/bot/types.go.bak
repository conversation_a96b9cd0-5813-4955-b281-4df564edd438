package types

import "encoding/json"

type Event struct {
	Type string                 `json:"type"`
	Data map[string]interface{} `json:"data"`
}

type Operator struct {
	OpenId  string `json:"open_id"`
	UserId  string `json:"user_id"`
	UnionId string `json:"union_id"`
}

type P2PJoinChat struct {
	ChatId                string   `json:"chat_id"`
	LastMessageCreateTime string   `json:"last_message_create_time"`
	OperatorId            Operator `json:"operator_id"`
}

// ImMessageReceiveEvent IM消息结构体
type ImMessageReceiveEvent struct {
	Sender struct {
		SenderID struct {
			UnionID string `json:"union_id"`
			UserID  string `json:"user_id"`
			OpenID  string `json:"open_id"`
		} `json:"sender_id"`
		SenderType string `json:"sender_type"`
		TenantKey  string `json:"tenant_key"`
	} `json:"sender"`
	Message struct {
		MessageID   string `json:"message_id"`
		RootID      string `json:"root_id"`
		ParentID    string `json:"parent_id"`
		CreateTime  string `json:"create_time"`
		UpdateTime  string `json:"update_time"`
		ChatID      string `json:"chat_id"`
		ThreadID    string `json:"thread_id"`
		ChatType    string `json:"chat_type"`
		MessageType string `json:"message_type"`
		Content     string `json:"content"`
		Mentions    []struct {
			Key string `json:"key"`
			ID  struct {
				UnionID string `json:"union_id"`
				UserID  string `json:"user_id"`
				OpenID  string `json:"open_id"`
			} `json:"id"`
			Name      string `json:"name"`
			TenantKey string `json:"tenant_key"`
		} `json:"mentions"`
		UserAgent string `json:"user_agent"`
	} `json:"message"`
}

type FileData struct {
	WikiType  string `json:"wiki_type,omitempty"`
	MessageId string `json:"message_id,omitempty"`
	FileKey   string `json:"file_key"`
	FileName  string `json:"file_name"`
}

type TextData struct {
	MessageId string `json:"message_id,omitempty"`
	Text      string `json:"text"`
}

type HistoryMessage struct {
	Files []*FileData //  历史消息文件列表
	Texts []*TextData //  历史消息文本列表
}

type Card struct {
	Type string   `json:"type"`
	Data CardData `json:"data"`
}

type CardData struct {
	TemplateID          string         `json:"template_id"`
	TemplateVersionName string         `json:"template_version_name"`
	TemplateVariable    map[string]any `json:"template_variable"`
}

type CardMessage struct {
	Card Card `json:"card"`
}

type DirectiveResponse struct {
	Data any `json:"data"`
}

type WorkFlowRes struct {
	Message string `json:"message"`
}

func (w *WorkFlowRes) Unmarshal(data []byte) error {
	return json.Unmarshal(data, w)
}
