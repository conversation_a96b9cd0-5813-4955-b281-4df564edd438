package feishu

import (
	"context"
	"errors"
)

// MessageProcessor 消息处理器接口，支持插件化架构
type MessageProcessor interface {
	// CanHandle 判断是否能处理该类型的消息
	CanHandle(messageType string) bool

	// Process 处理消息并返回处理结果
	Process(ctx context.Context, message *ImMessageReceiveEvent) (*ProcessResult, error)

	// GetPriority 获取处理器优先级，数值越小优先级越高
	GetPriority() int
}

// ContentRetriever 内容检索器接口，支持策略模式
type ContentRetriever interface {
	// CanRetrieve 判断是否能检索该类型的内容
	CanRetrieve(contentType string, source interface{}) bool

	// Retrieve 检索内容并返回结构化数据
	Retrieve(ctx context.Context, source interface{}) (*ContentResult, error)

	// GetType 获取检索器类型标识
	GetType() string
}

// ContentParser 内容解析器接口，用于解析不同格式的文件
type ContentParser interface {
	// SupportedFormats 返回支持的文件格式列表
	SupportedFormats() []string

	// Parse 解析内容并提取文本
	Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error)
}

// ProcessResult 消息处理结果
type ProcessResult struct {
	// Content 处理后的内容
	Content string

	// Files 相关文件信息
	Files []*FileData

	// Metadata 额外的元数据
	Metadata map[string]interface{}

	// ShouldContinue 是否应该继续处理（用于责任链模式）
	ShouldContinue bool
}

// ContentResult 内容检索结果
type ContentResult struct {
	// Text 提取的文本内容
	Text string

	// Title 内容标题
	Title string

	// Type 内容类型
	Type string

	// Size 内容大小
	Size int64

	// Metadata 内容元数据
	Metadata map[string]interface{}

	// RawContent 原始内容（可选）
	RawContent []byte
}

// ParsedContent 解析后的内容
type ParsedContent struct {
	// Text 提取的纯文本
	Text string

	// Structure 文档结构信息
	Structure map[string]interface{}

	// Images 图片信息
	Images []ImageInfo

	// Tables 表格数据
	Tables []TableData

	// Links 链接信息
	Links []LinkInfo
}

// ImageInfo 图片信息
type ImageInfo struct {
	URL      string            `json:"url"`
	Alt      string            `json:"alt"`
	Caption  string            `json:"caption"`
	Metadata map[string]string `json:"metadata"`
}

// TableData 表格数据
type TableData struct {
	Headers  []string          `json:"headers"`
	Rows     [][]string        `json:"rows"`
	Caption  string            `json:"caption"`
	Metadata map[string]string `json:"metadata"`
}

// LinkInfo 链接信息
type LinkInfo struct {
	URL   string `json:"url"`
	Text  string `json:"text"`
	Title string `json:"title"`
}

// MessageProcessorRegistry 消息处理器注册表
type MessageProcessorRegistry struct {
	processors []MessageProcessor
}

// NewMessageProcessorRegistry 创建消息处理器注册表
func NewMessageProcessorRegistry() *MessageProcessorRegistry {
	return &MessageProcessorRegistry{
		processors: make([]MessageProcessor, 0),
	}
}

// Register 注册消息处理器
func (r *MessageProcessorRegistry) Register(processor MessageProcessor) {
	r.processors = append(r.processors, processor)

	// 按优先级排序
	for i := len(r.processors) - 1; i > 0; i-- {
		if r.processors[i].GetPriority() < r.processors[i-1].GetPriority() {
			r.processors[i], r.processors[i-1] = r.processors[i-1], r.processors[i]
		} else {
			break
		}
	}
}

// Process 处理消息，按优先级顺序尝试处理器
func (r *MessageProcessorRegistry) Process(ctx context.Context, message *ImMessageReceiveEvent) (*ProcessResult, error) {
	for _, processor := range r.processors {
		if processor.CanHandle(message.Message.MessageType) {
			result, err := processor.Process(ctx, message)
			if err != nil {
				continue // 尝试下一个处理器
			}
			if result != nil && !result.ShouldContinue {
				return result, nil
			}
		}
	}

	return nil, ErrNoSuitableProcessor
}

// ContentRetrieverRegistry 内容检索器注册表
type ContentRetrieverRegistry struct {
	retrievers map[string]ContentRetriever
}

// NewContentRetrieverRegistry 创建内容检索器注册表
func NewContentRetrieverRegistry() *ContentRetrieverRegistry {
	return &ContentRetrieverRegistry{
		retrievers: make(map[string]ContentRetriever),
	}
}

// Register 注册内容检索器
func (r *ContentRetrieverRegistry) Register(retriever ContentRetriever) {
	r.retrievers[retriever.GetType()] = retriever
}

// Retrieve 检索内容
func (r *ContentRetrieverRegistry) Retrieve(ctx context.Context, contentType string, source interface{}) (*ContentResult, error) {
	for _, retriever := range r.retrievers {
		if retriever.CanRetrieve(contentType, source) {
			return retriever.Retrieve(ctx, source)
		}
	}

	return nil, ErrNoSuitableRetriever
}

// ContentParserRegistry 内容解析器注册表
type ContentParserRegistry struct {
	parsers map[string]ContentParser
}

// NewContentParserRegistry 创建内容解析器注册表
func NewContentParserRegistry() *ContentParserRegistry {
	return &ContentParserRegistry{
		parsers: make(map[string]ContentParser),
	}
}

// Register 注册内容解析器
func (r *ContentParserRegistry) Register(format string, parser ContentParser) {
	r.parsers[format] = parser
}

// Parse 解析内容
func (r *ContentParserRegistry) Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error) {
	parser, exists := r.parsers[format]
	if !exists {
		return nil, ErrUnsupportedFormat
	}

	return parser.Parse(ctx, content, format)
}

// 错误定义
var (
	ErrNoSuitableProcessor = errors.New("没有找到合适的消息处理器")
	ErrNoSuitableRetriever = errors.New("没有找到合适的内容检索器")
	ErrUnsupportedFormat   = errors.New("不支持的文件格式")
	ErrContentNotFound     = errors.New("内容未找到")
	ErrInvalidSource       = errors.New("无效的内容源")
)
