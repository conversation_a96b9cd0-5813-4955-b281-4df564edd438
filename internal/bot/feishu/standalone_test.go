package feishu

import (
	"context"
	"fmt"
	"testing"
)

// StandaloneTest 独立测试，不依赖外部模块
func TestStandaloneFeishuBot(t *testing.T) {
	fmt.Println("🤖 FeishuBot 独立功能测试")
	fmt.Println("=" * 40)

	// 1. 测试配置结构
	t.Run("配置结构测试", func(t *testing.T) {
		config := &FeishuBotConfig{
			AppId:     "cli_test_12345",
			AppSecret: "secret_67890",
			BotName:   "测试机器人",
		}

		// 测试有效配置
		if err := config.Validate(); err != nil {
			t.<PERSON>rrorf("有效配置验证失败: %v", err)
		}

		// 测试配置获取方法
		if config.GetAppID() != "cli_test_12345" {
			t.<PERSON>rrorf("AppID获取错误: 期望 cli_test_12345, 实际 %s", config.GetAppID())
		}

		if config.GetBotName() != "测试机器人" {
			t.<PERSON><PERSON><PERSON>("BotName获取错误: 期望 测试机器人, 实际 %s", config.GetBotName())
		}

		fmt.Println("   ✅ 配置结构测试通过")
	})

	// 2. 测试无效配置
	t.Run("无效配置测试", func(t *testing.T) {
		invalidConfigs := []*FeishuBotConfig{
			{AppId: "", AppSecret: "secret", BotName: "bot"},     // 空AppId
			{AppId: "app", AppSecret: "", BotName: "bot"},       // 空AppSecret
			{AppId: "app", AppSecret: "secret", BotName: ""},    // 空BotName
		}

		for i, config := range invalidConfigs {
			if err := config.Validate(); err == nil {
				t.Errorf("无效配置 %d 应该返回错误", i)
			}
		}

		fmt.Println("   ✅ 无效配置测试通过")
	})

	// 3. 测试接口定义
	t.Run("接口定义测试", func(t *testing.T) {
		// 测试MessageProcessor接口
		processor := &TestMessageProcessor{}
		if !processor.CanHandle("test") {
			t.Error("测试处理器应该能处理test类型")
		}

		if processor.GetPriority() != 1 {
			t.Errorf("优先级错误: 期望 1, 实际 %d", processor.GetPriority())
		}

		// 测试ContentRetriever接口
		retriever := &TestContentRetriever{}
		if !retriever.CanRetrieve("test", nil) {
			t.Error("测试检索器应该能检索test类型")
		}

		if retriever.GetType() != "test" {
			t.Errorf("类型错误: 期望 test, 实际 %s", retriever.GetType())
		}

		fmt.Println("   ✅ 接口定义测试通过")
	})

	// 4. 测试注册表功能
	t.Run("注册表功能测试", func(t *testing.T) {
		// 测试消息处理器注册表
		registry := NewMessageProcessorRegistry()
		processor := &TestMessageProcessor{}
		registry.Register(processor)

		if len(registry.processors) != 1 {
			t.Errorf("处理器注册失败: 期望 1, 实际 %d", len(registry.processors))
		}

		// 测试内容检索器注册表
		retrievers := NewContentRetrieverRegistry()
		retriever := &TestContentRetriever{}
		retrievers.Register(retriever)

		if len(retrievers.retrievers) != 1 {
			t.Errorf("检索器注册失败: 期望 1, 实际 %d", len(retrievers.retrievers))
		}

		fmt.Println("   ✅ 注册表功能测试通过")
	})

	// 5. 测试错误定义
	t.Run("错误定义测试", func(t *testing.T) {
		errors := []error{
			ErrNoSuitableProcessor,
			ErrNoSuitableRetriever,
			ErrUnsupportedFormat,
			ErrContentNotFound,
			ErrInvalidSource,
		}

		for i, err := range errors {
			if err == nil {
				t.Errorf("错误 %d 不应该为nil", i)
			}
			if err.Error() == "" {
				t.Errorf("错误 %d 消息不应该为空", i)
			}
		}

		fmt.Println("   ✅ 错误定义测试通过")
	})

	fmt.Println("\n🎉 所有独立测试通过！")
}

// TestMessageProcessor 测试消息处理器
type TestMessageProcessor struct{}

func (p *TestMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "test"
}

func (p *TestMessageProcessor) Process(ctx context.Context, message interface{}) (*ProcessResult, error) {
	return &ProcessResult{
		Content:        "测试处理结果",
		Files:          []*FileData{},
		Metadata:       map[string]interface{}{},
		ShouldContinue: false,
	}, nil
}

func (p *TestMessageProcessor) GetPriority() int {
	return 1
}

// TestContentRetriever 测试内容检索器
type TestContentRetriever struct{}

func (r *TestContentRetriever) CanRetrieve(contentType string, source interface{}) bool {
	return contentType == "test"
}

func (r *TestContentRetriever) Retrieve(ctx context.Context, source interface{}) (*ContentResult, error) {
	return &ContentResult{
		Text:     "测试内容",
		Title:    "测试标题",
		Type:     "test",
		Size:     100,
		Metadata: map[string]interface{}{},
	}, nil
}

func (r *TestContentRetriever) GetType() string {
	return "test"
}

// TestContentParser 测试内容解析器
type TestContentParser struct{}

func (p *TestContentParser) SupportedFormats() []string {
	return []string{"test"}
}

func (p *TestContentParser) Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error) {
	return &ParsedContent{
		Text:      string(content),
		Structure: map[string]interface{}{"type": "test"},
		Images:    []ImageInfo{},
		Tables:    []TableData{},
		Links:     []LinkInfo{},
	}, nil
}

// BenchmarkProcessorRegistry 性能基准测试
func BenchmarkProcessorRegistry(b *testing.B) {
	registry := NewMessageProcessorRegistry()
	processor := &TestMessageProcessor{}
	registry.Register(processor)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := registry.Process(ctx, &TestMessage{MessageType: "test"})
		if err != nil {
			b.Fatalf("处理失败: %v", err)
		}
	}
}

// TestMessage 测试消息结构
type TestMessage struct {
	MessageType string
}

// 实现必要的接口方法
func (m *TestMessage) GetMessageType() string {
	return m.MessageType
}

// TestFeishuBotCreation 测试机器人创建
func TestFeishuBotCreation(t *testing.T) {
	ctx := context.Background()
	
	config := &FeishuBotConfig{
		AppId:     "cli_test_app_id",
		AppSecret: "test_app_secret",
		BotName:   "测试机器人",
	}

	// 测试基本创建（不需要外部依赖）
	bot := &FeishuBot{
		ctx:    ctx,
		config: config,
		logger: &MockLogger{},
	}

	// 初始化组件
	bot.initializeComponents()

	// 验证组件初始化
	if bot.contentParsers == nil {
		t.Error("内容解析器未初始化")
	}

	if bot.contentRetrievers == nil {
		t.Error("内容检索器未初始化")
	}

	if bot.messageProcessors == nil {
		t.Error("消息处理器未初始化")
	}

	fmt.Println("✅ 机器人创建测试通过")
}

// MockLogger 模拟日志器
type MockLogger struct{}

func (l *MockLogger) Debug(args ...interface{})                 {}
func (l *MockLogger) Debugf(template string, args ...interface{}) {}
func (l *MockLogger) Debugv(args ...interface{})               {}
func (l *MockLogger) Debugw(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Error(args ...interface{})                {}
func (l *MockLogger) Errorf(template string, args ...interface{}) {}
func (l *MockLogger) Errorv(args ...interface{})               {}
func (l *MockLogger) Errorw(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Fatal(args ...interface{})                {}
func (l *MockLogger) Fatalf(template string, args ...interface{}) {}
func (l *MockLogger) Fatalv(args ...interface{})               {}
func (l *MockLogger) Fatalw(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Info(args ...interface{})                 {}
func (l *MockLogger) Infof(template string, args ...interface{}) {}
func (l *MockLogger) Infov(args ...interface{})                {}
func (l *MockLogger) Infow(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Slow(args ...interface{})                 {}
func (l *MockLogger) Slowf(template string, args ...interface{}) {}
func (l *MockLogger) Slowv(args ...interface{})                {}
func (l *MockLogger) Sloww(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Stack(args ...interface{})                {}
func (l *MockLogger) Stackf(template string, args ...interface{}) {}
func (l *MockLogger) Stackv(args ...interface{})               {}
func (l *MockLogger) Stackw(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Stat(args ...interface{})                 {}
func (l *MockLogger) Statf(template string, args ...interface{}) {}
func (l *MockLogger) Statv(args ...interface{})                {}
func (l *MockLogger) Statw(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Severe(args ...interface{})               {}
func (l *MockLogger) Severef(template string, args ...interface{}) {}
func (l *MockLogger) Severev(args ...interface{})              {}
func (l *MockLogger) Severew(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) Warn(args ...interface{})                 {}
func (l *MockLogger) Warnf(template string, args ...interface{}) {}
func (l *MockLogger) Warnv(args ...interface{})                {}
func (l *MockLogger) Warnw(msg string, keysAndValues ...interface{}) {}
func (l *MockLogger) WithCallerSkip(skip int) interface{}      { return l }
func (l *MockLogger) WithContext(ctx context.Context) interface{} { return l }
func (l *MockLogger) WithDuration(d interface{}) interface{}   { return l }
func (l *MockLogger) WithFields(fields ...interface{}) interface{} { return l }

// 简化的结构定义，避免外部依赖
type FileData struct {
	FileKey   string `json:"file_key"`
	FileName  string `json:"file_name"`
	FileType  string `json:"file_type"`
	MessageId string `json:"message_id,omitempty"`
	WikiType  string `json:"wiki_type,omitempty"`
}
