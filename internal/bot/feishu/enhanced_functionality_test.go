package feishu

import (
	"context"
	"testing"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/zeromicro/go-zero/core/logx"
)

// TestEnhancedContentRetrievers 测试增强的内容检索器功能
func TestEnhancedContentRetrievers(t *testing.T) {
	// 测试文件内容检索器
	t.Run("FileContentRetriever", func(t *testing.T) {
		// 跳过需要真实API客户端的测试
		t.Skip("需要真实API客户端，跳过测试")
	})

	// 测试Wiki内容检索器
	t.Run("WikiContentRetriever", func(t *testing.T) {
		// 跳过需要真实API客户端的测试
		t.Skip("需要真实API客户端，跳过测试")
	})

	// 测试URL内容检索器
	t.Run("URLContentRetriever", func(t *testing.T) {
		// 跳过需要真实API客户端的测试
		t.Skip("需要真实API客户端，跳过测试")
	})
}

// TestEnhancedMessageProcessors 测试增强的消息处理器功能
func TestEnhancedMessageProcessors(t *testing.T) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)
	
	// 创建内容检索器注册表
	retrieverRegistry := NewContentRetrieverRegistry()
	
	// 测试文本消息处理器
	t.Run("TextMessageProcessor", func(t *testing.T) {
		processor := NewTextMessageProcessor(retrieverRegistry, logger)
		
		// 测试能否处理text类型
		if !processor.CanHandle("text") {
			t.Error("TextMessageProcessor应该能处理text类型")
		}
		
		if processor.CanHandle("file") {
			t.Error("TextMessageProcessor不应该处理file类型")
		}
		
		// 测试优先级
		if processor.GetPriority() != 10 {
			t.Errorf("TextMessageProcessor优先级应该是10，实际: %d", processor.GetPriority())
		}
	})
	
	// 测试文件消息处理器
	t.Run("FileMessageProcessor", func(t *testing.T) {
		processor := NewFileMessageProcessor(retrieverRegistry, logger)
		
		// 测试能否处理file类型
		if !processor.CanHandle("file") {
			t.Error("FileMessageProcessor应该能处理file类型")
		}
		
		if processor.CanHandle("text") {
			t.Error("FileMessageProcessor不应该处理text类型")
		}
		
		// 测试优先级
		if processor.GetPriority() != 5 {
			t.Errorf("FileMessageProcessor优先级应该是5，实际: %d", processor.GetPriority())
		}
	})
	
	// 测试图片消息处理器
	t.Run("ImageMessageProcessor", func(t *testing.T) {
		processor := NewImageMessageProcessor(logger)
		
		// 测试能否处理image类型
		if !processor.CanHandle("image") {
			t.Error("ImageMessageProcessor应该能处理image类型")
		}
		
		if processor.CanHandle("text") {
			t.Error("ImageMessageProcessor不应该处理text类型")
		}
		
		// 测试优先级
		if processor.GetPriority() != 8 {
			t.Errorf("ImageMessageProcessor优先级应该是8，实际: %d", processor.GetPriority())
		}
	})
	
	// 测试富文本消息处理器
	t.Run("PostMessageProcessor", func(t *testing.T) {
		processor := NewPostMessageProcessor(logger)
		
		// 测试能否处理post类型
		if !processor.CanHandle("post") {
			t.Error("PostMessageProcessor应该能处理post类型")
		}
		
		if processor.CanHandle("text") {
			t.Error("PostMessageProcessor不应该处理text类型")
		}
		
		// 测试优先级
		if processor.GetPriority() != 7 {
			t.Errorf("PostMessageProcessor优先级应该是7，实际: %d", processor.GetPriority())
		}
	})
}

// TestDefaultRegistries 测试默认注册表创建
func TestDefaultRegistries(t *testing.T) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)

	// 测试默认内容解析器注册表
	t.Run("DefaultContentParserRegistry", func(t *testing.T) {
		registry := DefaultContentParserRegistry(logger)

		// 检查注册表是否创建成功
		if registry == nil {
			t.Error("默认内容解析器注册表创建失败")
		}

		// 检查是否有解析器注册
		if len(registry.parsers) == 0 {
			t.Error("默认注册表中没有注册任何解析器")
		}
	})
	
	// 测试默认消息处理器注册表
	t.Run("DefaultMessageProcessorRegistry", func(t *testing.T) {
		retrieverRegistry := NewContentRetrieverRegistry()
		registry := DefaultMessageProcessorRegistry(retrieverRegistry, logger)
		
		// 检查是否注册了所有预期的处理器
		expectedTypes := []string{"text", "file", "image", "post"}
		for _, expectedType := range expectedTypes {
			found := false
			for _, processor := range registry.processors {
				if processor.CanHandle(expectedType) {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("默认注册表中缺少处理%s类型的处理器", expectedType)
			}
		}
	})
}

// MockAPIClient 模拟API客户端
type MockAPIClient struct{}

func (m *MockAPIClient) GetMessage(ctx context.Context, messageId string) (interface{}, error) {
	return nil, nil
}

func (m *MockAPIClient) GetWikiNode(ctx context.Context, token string) (interface{}, error) {
	return nil, nil
}

func (m *MockAPIClient) GetWikiNodeContent(ctx context.Context, token string) (map[string]interface{}, error) {
	return map[string]interface{}{
		"title": "测试Wiki",
		"type":  "doc",
	}, nil
}

func (m *MockAPIClient) GetFile(ctx context.Context, fileToken string) (map[string]interface{}, error) {
	return map[string]interface{}{
		"name": "测试文件.txt",
		"type": "text/plain",
	}, nil
}

// TestProcessResultStructure 测试ProcessResult结构
func TestProcessResultStructure(t *testing.T) {
	result := &ProcessResult{
		Content:        "测试内容",
		Files:          []*bot.FileData{},
		Metadata:       map[string]interface{}{"test": "value"},
		ShouldContinue: false,
	}
	
	if result.Content != "测试内容" {
		t.Errorf("Content字段错误，期望: 测试内容, 实际: %s", result.Content)
	}
	
	if result.ShouldContinue != false {
		t.Error("ShouldContinue字段应该为false")
	}
	
	if result.Metadata["test"] != "value" {
		t.Error("Metadata字段设置错误")
	}
}

// TestContentResultStructure 测试ContentResult结构
func TestContentResultStructure(t *testing.T) {
	result := &ContentResult{
		Text:     "测试文本",
		Title:    "测试标题",
		Type:     "text",
		Size:     100,
		Metadata: map[string]interface{}{"source": "test"},
	}
	
	if result.Text != "测试文本" {
		t.Errorf("Text字段错误，期望: 测试文本, 实际: %s", result.Text)
	}
	
	if result.Title != "测试标题" {
		t.Errorf("Title字段错误，期望: 测试标题, 实际: %s", result.Title)
	}
	
	if result.Size != 100 {
		t.Errorf("Size字段错误，期望: 100, 实际: %d", result.Size)
	}
}
