package feishu

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/zeromicro/go-zero/core/logx"
)

// TextMessageProcessor 文本消息处理器
type TextMessageProcessor struct {
	contentRetriever *ContentRetrieverRegistry
	logger           logx.Logger
}

// NewTextMessageProcessor 创建文本消息处理器
func NewTextMessageProcessor(contentRetriever *ContentRetrieverRegistry, logger logx.Logger) *TextMessageProcessor {
	return &TextMessageProcessor{
		contentRetriever: contentRetriever,
		logger:           logger,
	}
}

// CanHandle 判断是否能处理该类型的消息
func (p *TextMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "text"
}

// Process 处理文本消息
func (p *TextMessageProcessor) Process(ctx context.Context, message *bot.ImMessageReceiveEvent) (*ProcessResult, error) {
	var content struct {
		Text string `json:"text"`
	}
	
	if err := JSONUnmarshal([]byte(message.Message.Content), &content); err != nil {
		return nil, fmt.Errorf("解析文本消息内容失败: %w", err)
	}

	// 清理文本（移除@用户标记）
	cleanText := p.cleanText(content.Text)
	
	// 检测并处理文本中的链接
	files := make([]*bot.FileData, 0)
	metadata := make(map[string]interface{})
	
	// 检测飞书Wiki链接
	if wikiUrl := p.detectFeishuWiki(cleanText); wikiUrl != "" {
		contentResult, err := p.contentRetriever.Retrieve(ctx, "wiki", wikiUrl)
		if err != nil {
			p.logger.Warnf("检索Wiki内容失败: %v", err)
		} else {
			// 将Wiki内容添加到文件列表
			files = append(files, &bot.FileData{
				FileKey:  wikiUrl,
				FileName: contentResult.Title,
				WikiType: contentResult.Type,
			})
			metadata["wiki_content"] = contentResult
		}
	}

	return &ProcessResult{
		Content:        cleanText,
		Files:          files,
		Metadata:       metadata,
		ShouldContinue: false,
	}, nil
}

// GetPriority 获取处理器优先级
func (p *TextMessageProcessor) GetPriority() int {
	return 10 // 中等优先级
}

// cleanText 清理文本内容
func (p *TextMessageProcessor) cleanText(text string) string {
	// 移除@用户标记
	re := regexp.MustCompile(`@_user_[a-zA-Z0-9-]+`)
	text = re.ReplaceAllString(text, "")
	return strings.TrimSpace(text)
}

// detectFeishuWiki 检测飞书Wiki链接
func (p *TextMessageProcessor) detectFeishuWiki(text string) string {
	if !strings.Contains(text, "https://boke.feishu.cn/wiki/") {
		return ""
	}
	re := regexp.MustCompile(`https://boke\.feishu\.cn/wiki/[a-zA-Z0-9-]+`)
	return re.FindString(text)
}

// FileMessageProcessor 文件消息处理器
type FileMessageProcessor struct {
	contentRetriever *ContentRetrieverRegistry
	logger           logx.Logger
}

// NewFileMessageProcessor 创建文件消息处理器
func NewFileMessageProcessor(contentRetriever *ContentRetrieverRegistry, logger logx.Logger) *FileMessageProcessor {
	return &FileMessageProcessor{
		contentRetriever: contentRetriever,
		logger:           logger,
	}
}

// CanHandle 判断是否能处理该类型的消息
func (p *FileMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "file"
}

// Process 处理文件消息
func (p *FileMessageProcessor) Process(ctx context.Context, message *bot.ImMessageReceiveEvent) (*ProcessResult, error) {
	var fileData bot.FileData

	if err := JSONUnmarshal([]byte(message.Message.Content), &fileData); err != nil {
		return nil, fmt.Errorf("解析文件消息内容失败: %w", err)
	}

	fileData.MessageId = message.Message.MessageID

	// 检索文件内容
	contentResult, err := p.contentRetriever.Retrieve(ctx, "file", &fileData)
	if err != nil {
		p.logger.Warnf("检索文件内容失败: %v", err)
		// 即使检索失败，也返回基本的文件信息
		return &ProcessResult{
			Content:        fmt.Sprintf("文件: %s", fileData.FileName),
			Files:          []*bot.FileData{&fileData},
			Metadata:       map[string]interface{}{"error": err.Error()},
			ShouldContinue: false,
		}, nil
	}

	return &ProcessResult{
		Content: fmt.Sprintf("文件: %s\n内容摘要: %s",
			contentResult.Title,
			p.truncateText(contentResult.Text, 500)),
		Files:          []*bot.FileData{&fileData},
		Metadata:       map[string]interface{}{"file_content": contentResult},
		ShouldContinue: false,
	}, nil
}

// GetPriority 获取处理器优先级
func (p *FileMessageProcessor) GetPriority() int {
	return 5 // 高优先级
}

// truncateText 截断文本到指定长度
func (p *FileMessageProcessor) truncateText(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}
	return text[:maxLength] + "..."
}

// ImageMessageProcessor 图片消息处理器
type ImageMessageProcessor struct {
	logger logx.Logger
}

// NewImageMessageProcessor 创建图片消息处理器
func NewImageMessageProcessor(logger logx.Logger) *ImageMessageProcessor {
	return &ImageMessageProcessor{
		logger: logger,
	}
}

// CanHandle 判断是否能处理该类型的消息
func (p *ImageMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "image"
}

// Process 处理图片消息
func (p *ImageMessageProcessor) Process(ctx context.Context, message *bot.ImMessageReceiveEvent) (*ProcessResult, error) {
	var imageData struct {
		ImageKey string `json:"image_key"`
		Width    int    `json:"width,omitempty"`
		Height   int    `json:"height,omitempty"`
	}
	
	if err := JSONUnmarshal([]byte(message.Message.Content), &imageData); err != nil {
		return nil, fmt.Errorf("解析图片消息内容失败: %w", err)
	}

	// 创建文件数据
	fileData := &bot.FileData{
		FileKey:   imageData.ImageKey,
		FileName:  "图片",
		FileType:  "image",
		MessageId: message.Message.MessageID,
	}

	return &ProcessResult{
		Content: fmt.Sprintf("图片 (尺寸: %dx%d)", imageData.Width, imageData.Height),
		Files:   []*bot.FileData{fileData},
		Metadata: map[string]interface{}{
			"image_key": imageData.ImageKey,
			"width":     imageData.Width,
			"height":    imageData.Height,
		},
		ShouldContinue: false,
	}, nil
}

// GetPriority 获取处理器优先级
func (p *ImageMessageProcessor) GetPriority() int {
	return 8 // 中等优先级
}

// PostMessageProcessor 富文本消息处理器
type PostMessageProcessor struct {
	logger logx.Logger
}

// NewPostMessageProcessor 创建富文本消息处理器
func NewPostMessageProcessor(logger logx.Logger) *PostMessageProcessor {
	return &PostMessageProcessor{
		logger: logger,
	}
}

// CanHandle 判断是否能处理该类型的消息
func (p *PostMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "post"
}

// Process 处理富文本消息
func (p *PostMessageProcessor) Process(ctx context.Context, message *bot.ImMessageReceiveEvent) (*ProcessResult, error) {
	// 富文本消息的内容通常是复杂的JSON结构
	// 这里提供一个基础的处理实现
	
	content := message.Message.Content
	
	// 尝试提取纯文本内容（简化实现）
	text := p.extractTextFromPost(content)
	
	return &ProcessResult{
		Content:        text,
		Files:          []*bot.FileData{},
		Metadata:       map[string]interface{}{"raw_post": content},
		ShouldContinue: false,
	}, nil
}

// GetPriority 获取处理器优先级
func (p *PostMessageProcessor) GetPriority() int {
	return 7 // 中等优先级
}

// extractTextFromPost 从富文本消息中提取纯文本
func (p *PostMessageProcessor) extractTextFromPost(content string) string {
	// 这是一个简化的实现，实际中需要根据飞书富文本的具体格式来解析
	var postData map[string]interface{}
	
	if err := json.Unmarshal([]byte(content), &postData); err != nil {
		p.logger.Warnf("解析富文本消息失败: %v", err)
		return "富文本消息"
	}
	
	// 递归提取文本内容
	return p.extractTextRecursive(postData)
}

// extractTextRecursive 递归提取文本内容
func (p *PostMessageProcessor) extractTextRecursive(data interface{}) string {
	var result strings.Builder
	
	switch v := data.(type) {
	case map[string]interface{}:
		if text, ok := v["text"].(string); ok {
			result.WriteString(text)
		}
		for _, value := range v {
			result.WriteString(p.extractTextRecursive(value))
		}
	case []interface{}:
		for _, item := range v {
			result.WriteString(p.extractTextRecursive(item))
		}
	case string:
		result.WriteString(v)
	}
	
	return result.String()
}

// DefaultMessageProcessorRegistry 创建默认的消息处理器注册表
func DefaultMessageProcessorRegistry(contentRetriever *ContentRetrieverRegistry, logger logx.Logger) *MessageProcessorRegistry {
	registry := NewMessageProcessorRegistry()
	
	// 注册各种消息处理器
	registry.Register(NewTextMessageProcessor(contentRetriever, logger))
	registry.Register(NewFileMessageProcessor(contentRetriever, logger))
	registry.Register(NewImageMessageProcessor(logger))
	registry.Register(NewPostMessageProcessor(logger))
	
	return registry
}
