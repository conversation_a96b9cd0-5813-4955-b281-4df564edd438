package feishu

import (
	"context"
	"fmt"
	"log"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/svc"
)

// ExampleEnhancedContentRetrieval 演示增强的内容检索功能
func ExampleEnhancedContentRetrieval() {
	ctx := context.Background()

	// 创建服务上下文（需要完整配置）
	svcCtx := &svc.ServiceContext{} // 实际使用中需要完整初始化

	// 创建飞书机器人配置
	config := &FeishuBotConfig{
		AppId:     "cli_your_app_id",
		AppSecret: "your_app_secret",
		BotName:   "智能助手",
	}

	// 创建机器人实例
	feishuBot := NewFeishuBot(ctx, config, svcCtx)
	
	// 初始化机器人
	if err := feishuBot.Initialize(ctx); err != nil {
		log.Fatalf("初始化失败: %v", err)
	}

	fmt.Println("=== 增强内容检索功能演示 ===")

	// 1. 演示文件内容检索
	demonstrateFileContentRetrieval(ctx, feishuBot)

	// 2. 演示Wiki内容检索
	demonstrateWikiContentRetrieval(ctx, feishuBot)

	// 3. 演示URL内容检索
	demonstrateURLContentRetrieval(ctx, feishuBot)

	// 4. 演示消息处理器集成
	demonstrateMessageProcessorIntegration(ctx, feishuBot)
}

// demonstrateFileContentRetrieval 演示文件内容检索
func demonstrateFileContentRetrieval(ctx context.Context, bot *FeishuBot) {
	fmt.Println("\n1. 文件内容检索演示:")

	// 模拟文件数据
	fileData := &bot.FileData{
		FileKey:  "example_file_key_123",
		FileName: "测试文档.docx",
		FileType: "docx",
	}

	// 使用文件内容检索器
	result, err := bot.contentRetrievers.Retrieve(ctx, "file", fileData)
	if err != nil {
		fmt.Printf("   ❌ 文件内容检索失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 文件标题: %s\n", result.Title)
	fmt.Printf("   📄 文件类型: %s\n", result.Type)
	fmt.Printf("   📊 文件大小: %d bytes\n", result.Size)
	fmt.Printf("   📝 内容摘要: %s\n", truncateString(result.Text, 100))
	
	// 显示元数据
	if metadata, ok := result.Metadata["structure"]; ok {
		fmt.Printf("   🏗️  文档结构: %v\n", metadata)
	}
	if images, ok := result.Metadata["images"].([]ImageInfo); ok && len(images) > 0 {
		fmt.Printf("   🖼️  包含图片: %d 张\n", len(images))
	}
	if tables, ok := result.Metadata["tables"].([]TableData); ok && len(tables) > 0 {
		fmt.Printf("   📋 包含表格: %d 个\n", len(tables))
	}
}

// demonstrateWikiContentRetrieval 演示Wiki内容检索
func demonstrateWikiContentRetrieval(ctx context.Context, bot *FeishuBot) {
	fmt.Println("\n2. Wiki内容检索演示:")

	// 模拟Wiki URL
	wikiUrl := "https://boke.feishu.cn/wiki/ABC123DEF456"

	// 使用Wiki内容检索器
	result, err := bot.contentRetrievers.Retrieve(ctx, "wiki", wikiUrl)
	if err != nil {
		fmt.Printf("   ❌ Wiki内容检索失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ Wiki标题: %s\n", result.Title)
	fmt.Printf("   📄 文档类型: %s\n", result.Type)
	fmt.Printf("   📝 内容摘要: %s\n", truncateString(result.Text, 150))
	
	// 显示Wiki特有的元数据
	if objToken, ok := result.Metadata["obj_token"].(string); ok {
		fmt.Printf("   🔗 对象Token: %s\n", objToken)
	}
	if spaceID, ok := result.Metadata["space_id"].(string); ok {
		fmt.Printf("   🏠 空间ID: %s\n", spaceID)
	}
}

// demonstrateURLContentRetrieval 演示URL内容检索
func demonstrateURLContentRetrieval(ctx context.Context, bot *FeishuBot) {
	fmt.Println("\n3. URL内容检索演示:")

	// 包含Wiki链接的文本
	textWithWiki := "请查看这个重要文档: https://boke.feishu.cn/wiki/XYZ789ABC123 里面有详细说明"

	// 使用URL内容检索器
	result, err := bot.contentRetrievers.Retrieve(ctx, "url", textWithWiki)
	if err != nil {
		fmt.Printf("   ❌ URL内容检索失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 检测到Wiki链接\n")
	fmt.Printf("   📄 文档标题: %s\n", result.Title)
	fmt.Printf("   📝 内容预览: %s\n", truncateString(result.Text, 100))
}

// demonstrateMessageProcessorIntegration 演示消息处理器集成
func demonstrateMessageProcessorIntegration(ctx context.Context, bot *FeishuBot) {
	fmt.Println("\n4. 消息处理器集成演示:")

	// 模拟包含文件的消息
	fileMessage := &bot.ImMessageReceiveEvent{
		Message: bot.Message{
			MessageID:   "msg_file_123",
			MessageType: "file",
			Content:     `{"file_key":"test_file_key","file_name":"重要报告.xlsx","file_type":"xlsx"}`,
		},
	}

	// 处理文件消息
	result, err := bot.messageProcessors.Process(ctx, fileMessage)
	if err != nil {
		fmt.Printf("   ❌ 文件消息处理失败: %v\n", err)
	} else {
		fmt.Printf("   ✅ 文件消息处理成功\n")
		fmt.Printf("   📄 处理结果: %s\n", result.Content)
		fmt.Printf("   📁 相关文件: %d 个\n", len(result.Files))
	}

	// 模拟包含Wiki链接的文本消息
	textMessage := &bot.ImMessageReceiveEvent{
		Message: bot.Message{
			MessageID:   "msg_text_123",
			MessageType: "text",
			Content:     `{"text":"帮我分析一下这个文档 https://boke.feishu.cn/wiki/DOC123456 的内容"}`,
		},
	}

	// 处理文本消息
	result, err = bot.messageProcessors.Process(ctx, textMessage)
	if err != nil {
		fmt.Printf("   ❌ 文本消息处理失败: %v\n", err)
	} else {
		fmt.Printf("   ✅ 文本消息处理成功\n")
		fmt.Printf("   📄 处理结果: %s\n", result.Content)
		if wikiContent, ok := result.Metadata["wiki_content"]; ok {
			fmt.Printf("   📚 检测到Wiki内容: %v\n", wikiContent != nil)
		}
	}
}

// ExampleAPIUsagePatterns 演示API使用模式
func ExampleAPIUsagePatterns() {
	fmt.Println("\n=== API使用模式演示 ===")

	// 1. 错误处理模式
	fmt.Println("\n1. 错误处理模式:")
	fmt.Println("   - 使用统一的APIClient处理所有API调用")
	fmt.Println("   - 自动重试机制（Drive API失败时尝试IM API）")
	fmt.Println("   - 详细的错误日志和状态码处理")
	fmt.Println("   - 优雅降级（解析失败时返回基本信息）")

	// 2. 认证和权限
	fmt.Println("\n2. 认证和权限:")
	fmt.Println("   - 支持tenant_access_token和user_access_token")
	fmt.Println("   - 自动选择合适的认证方式")
	fmt.Println("   - 权限检查和错误提示")

	// 3. 速率限制
	fmt.Println("\n3. 速率限制:")
	fmt.Println("   - 遵循官方API速率限制（100次/秒）")
	fmt.Println("   - 内置重试和退避机制")
	fmt.Println("   - 批量操作优化")

	// 4. 内容解析
	fmt.Println("\n4. 内容解析:")
	fmt.Println("   - 支持多种文件格式（DOCX、XLSX、PDF等）")
	fmt.Println("   - 结构化内容提取（文本、表格、图片、链接）")
	fmt.Println("   - 智能格式检测和转换")

	// 5. 扩展性
	fmt.Println("\n5. 扩展性:")
	fmt.Println("   - 插件化架构，易于添加新的内容类型")
	fmt.Println("   - 策略模式支持不同的检索策略")
	fmt.Println("   - 接口驱动的设计，便于测试和模拟")
}

// truncateString 截断字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// ExampleBestPractices 最佳实践示例
func ExampleBestPractices() {
	fmt.Println("\n=== 最佳实践 ===")

	fmt.Println("\n1. 性能优化:")
	fmt.Println("   - 使用连接池和缓存")
	fmt.Println("   - 异步处理大文件")
	fmt.Println("   - 分页处理大量数据")
	fmt.Println("   - 智能内容截断")

	fmt.Println("\n2. 错误处理:")
	fmt.Println("   - 多级降级策略")
	fmt.Println("   - 详细的错误日志")
	fmt.Println("   - 用户友好的错误消息")
	fmt.Println("   - 自动重试机制")

	fmt.Println("\n3. 安全性:")
	fmt.Println("   - 权限验证")
	fmt.Println("   - 输入验证和清理")
	fmt.Println("   - 敏感信息过滤")
	fmt.Println("   - 访问日志记录")

	fmt.Println("\n4. 可维护性:")
	fmt.Println("   - 模块化设计")
	fmt.Println("   - 接口抽象")
	fmt.Println("   - 单元测试覆盖")
	fmt.Println("   - 文档和示例")
}
