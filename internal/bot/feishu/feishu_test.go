package feishu

import (
	"context"
	"testing"
	"time"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFeishuBotConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  *FeishuBotConfig
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid config",
			config: &FeishuBotConfig{
				AppId:     "cli_test123456789",
				AppSecret: "test_secret",
				BotName:   "测试机器人",
			},
			wantErr: false,
		},
		{
			name: "missing app id",
			config: &FeishuBotConfig{
				AppSecret: "test_secret",
				BotName:   "测试机器人",
			},
			wantErr: true,
			errMsg:  "飞书应用ID不能为空",
		},
		{
			name: "missing app secret",
			config: &FeishuBotConfig{
				AppId:   "cli_test123456789",
				BotName: "测试机器人",
			},
			wantErr: true,
			errMsg:  "飞书应用密钥不能为空",
		},
		{
			name: "missing bot name",
			config: &FeishuBotConfig{
				AppId:     "cli_test123456789",
				AppSecret: "test_secret",
			},
			wantErr: true,
			errMsg:  "机器人名称不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestFeishuBotConfig_Getters(t *testing.T) {
	config := &FeishuBotConfig{
		AppId:      "cli_test123456789",
		AppSecret:  "test_secret",
		BotName:    "测试机器人",
		WebhookURL: "https://test.com/webhook",
	}

	assert.Equal(t, "cli_test123456789", config.GetAppID())
	assert.Equal(t, "test_secret", config.GetAppSecret())
	assert.Equal(t, "测试机器人", config.GetBotName())
	assert.Equal(t, "https://test.com/webhook", config.GetWebhookURL())
}

func TestNewFeishuBot(t *testing.T) {
	ctx := context.Background()
	config := &FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
	}

	// Note: In a real test, you would need a properly initialized ServiceContext
	// For this test, we'll use nil and test only the basic construction
	bot := NewFeishuBot(ctx, config, nil)

	assert.NotNil(t, bot)
	assert.Equal(t, config, bot.config)
	assert.Equal(t, ctx, bot.ctx)
	assert.False(t, bot.isInitialized)
	assert.False(t, bot.isHealthy)
}

func TestNewFeishuBotFromConfig(t *testing.T) {
	ctx := context.Background()
	appId := "cli_test123456789"
	appSecret := "test_secret"
	botName := "测试机器人"

	bot := NewFeishuBotFromConfig(ctx, appId, appSecret, botName, nil)

	assert.NotNil(t, bot)
	assert.Equal(t, appId, bot.config.AppId)
	assert.Equal(t, appSecret, bot.config.AppSecret)
	assert.Equal(t, botName, bot.config.BotName)
}

func TestFeishuBot_GetPlatform(t *testing.T) {
	ctx := context.Background()
	config := &FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
	}

	bot := NewFeishuBot(ctx, config, nil)
	assert.Equal(t, bot.BotPlatform("feishu"), bot.GetPlatform())
}

func TestFeishuBot_IsHealthy(t *testing.T) {
	ctx := context.Background()
	config := &FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
	}

	bot := NewFeishuBot(ctx, config, nil)
	
	// Initially not healthy
	assert.False(t, bot.IsHealthy())
	
	// After initialization (simulated)
	bot.isInitialized = true
	bot.isHealthy = true
	assert.True(t, bot.IsHealthy())
	
	// After cleanup
	bot.Cleanup()
	assert.False(t, bot.IsHealthy())
}

func TestFeishuBot_dealText(t *testing.T) {
	ctx := context.Background()
	config := &FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
	}

	bot := NewFeishuBot(ctx, config, nil)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "normal text",
			input:    "Hello world",
			expected: "Hello world",
		},
		{
			name:     "text with mention",
			input:    "@_user_123456 Hello world",
			expected: "Hello world",
		},
		{
			name:     "text with multiple mentions",
			input:    "@_user_123456 @_user_789012 Hello world",
			expected: "Hello world",
		},
		{
			name:     "text with mention in middle",
			input:    "Hello @_user_123456 world",
			expected: "Hello  world",
		},
		{
			name:     "only mention",
			input:    "@_user_123456",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := bot.dealText(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFeishuBot_isFeiShuWiki(t *testing.T) {
	ctx := context.Background()
	config := &FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
	}

	bot := NewFeishuBot(ctx, config, nil)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "valid wiki url",
			input:    "请查看这个文档 https://boke.feishu.cn/wiki/ABC123DEF456",
			expected: "https://boke.feishu.cn/wiki/ABC123DEF456",
		},
		{
			name:     "no wiki url",
			input:    "这是普通文本",
			expected: "",
		},
		{
			name:     "other url",
			input:    "请查看 https://example.com",
			expected: "",
		},
		{
			name:     "multiple wiki urls",
			input:    "文档1: https://boke.feishu.cn/wiki/ABC123 文档2: https://boke.feishu.cn/wiki/DEF456",
			expected: "https://boke.feishu.cn/wiki/ABC123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := bot.isFeiShuWiki(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestFeishuBot_InterfaceCompliance 验证FeishuBot实现了Bot接口
func TestFeishuBot_InterfaceCompliance(t *testing.T) {
	ctx := context.Background()
	config := &FeishuBotConfig{
		AppId:     "cli_test123456789",
		AppSecret: "test_secret",
		BotName:   "测试机器人",
	}

	var _ bot.Bot = NewFeishuBot(ctx, config, nil)
	// 如果编译通过，说明接口实现正确
}
