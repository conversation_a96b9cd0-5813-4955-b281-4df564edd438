package feishu

import (
	"context"
	"testing"

	"github.com/run-bigpig/hongdou/internal/bot"
)

// TestFeishuBotCompilation 测试飞书机器人编译和基本功能
func TestFeishuBotCompilation(t *testing.T) {
	// 测试配置创建
	config := &FeishuBotConfig{
		AppId:     "test_app_id",
		AppSecret: "test_app_secret",
		BotName:   "测试机器人",
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		t.Errorf("配置验证失败: %v", err)
	}

	// 测试机器人创建
	ctx := context.Background()
	bot := NewFeishuBot(ctx, config, nil)
	if bot == nil {
		t.Error("机器人创建失败")
	}

	// 测试机器人基本属性
	if bot.config.AppId != "test_app_id" {
		t.<PERSON><PERSON><PERSON>("AppId错误，期望: test_app_id, 实际: %s", bot.config.AppId)
	}
}

// TestMessageProcessorRegistry 测试消息处理器注册表
func TestMessageProcessorRegistry(t *testing.T) {
	registry := NewMessageProcessorRegistry()
	if registry == nil {
		t.Error("消息处理器注册表创建失败")
	}

	// 创建模拟处理器
	mockProcessor := &MockMessageProcessor{}
	registry.Register(mockProcessor)

	// 测试处理器数量
	if len(registry.processors) != 1 {
		t.Errorf("处理器数量错误，期望: 1, 实际: %d", len(registry.processors))
	}
}

// TestContentRetrieverRegistry 测试内容检索器注册表
func TestContentRetrieverRegistry(t *testing.T) {
	registry := NewContentRetrieverRegistry()
	if registry == nil {
		t.Error("内容检索器注册表创建失败")
	}

	// 创建模拟检索器
	mockRetriever := &MockContentRetriever{}
	registry.Register(mockRetriever)

	// 测试检索器数量
	if len(registry.retrievers) != 1 {
		t.Errorf("检索器数量错误，期望: 1, 实际: %d", len(registry.retrievers))
	}
}

// MockMessageProcessor 模拟消息处理器
type MockMessageProcessor struct{}

func (p *MockMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "test"
}

func (p *MockMessageProcessor) Process(ctx context.Context, message *bot.ImMessageReceiveEvent) (*ProcessResult, error) {
	return &ProcessResult{
		Content:        "测试处理结果",
		Files:          []*bot.FileData{},
		Metadata:       map[string]interface{}{},
		ShouldContinue: false,
	}, nil
}

func (p *MockMessageProcessor) GetPriority() int {
	return 1
}

// MockContentRetriever 模拟内容检索器
type MockContentRetriever struct{}

func (r *MockContentRetriever) CanRetrieve(contentType string, source interface{}) bool {
	return contentType == "test"
}

func (r *MockContentRetriever) Retrieve(ctx context.Context, source interface{}) (*ContentResult, error) {
	return &ContentResult{
		Text:     "测试内容",
		Title:    "测试标题",
		Type:     "test",
		Size:     100,
		Metadata: map[string]interface{}{},
	}, nil
}

func (r *MockContentRetriever) GetType() string {
	return "test"
}

// TestFeishuConfigValidation 测试飞书配置验证
func TestFeishuConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  *FeishuBotConfig
		wantErr bool
	}{
		{
			name: "有效配置",
			config: &FeishuBotConfig{
				AppId:     "test_app_id",
				AppSecret: "test_app_secret",
				BotName:   "测试机器人",
			},
			wantErr: false,
		},
		{
			name: "缺少AppId",
			config: &FeishuBotConfig{
				AppSecret: "test_app_secret",
				BotName:   "测试机器人",
			},
			wantErr: true,
		},
		{
			name: "缺少AppSecret",
			config: &FeishuBotConfig{
				AppId:   "test_app_id",
				BotName: "测试机器人",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
