package feishu

import (
	"context"
	"testing"

	"github.com/run-bigpig/hongdou/internal/bot"
)

// TestBasicStructures 测试基本结构
func TestBasicStructures(t *testing.T) {
	t.Run("FeishuBotConfig", func(t *testing.T) {
		config := &FeishuBotConfig{
			AppId:     "cli_test_12345",
			AppSecret: "secret_67890",
			BotName:   "测试机器人",
		}

		// 测试有效配置
		if err := config.Validate(); err != nil {
			t.<PERSON>rf("有效配置验证失败: %v", err)
		}

		// 测试获取方法
		if config.GetAppID() != "cli_test_12345" {
			t.<PERSON>("AppID获取错误")
		}

		if config.GetBotName() != "测试机器人" {
			t.<PERSON>rrorf("BotName获取错误")
		}
	})

	t.<PERSON>("InvalidConfig", func(t *testing.T) {
		invalidConfigs := []*FeishuBotConfig{
			{AppId: "", AppSecret: "secret", BotName: "bot"},
			{AppId: "app", AppSecret: "", BotName: "bot"},
			{AppId: "app", AppSecret: "secret", BotName: ""},
		}

		for i, config := range invalidConfigs {
			if err := config.Validate(); err == nil {
				t.Errorf("无效配置 %d 应该返回错误", i)
			}
		}
	})
}

// TestInterfaces 测试接口定义
func TestInterfaces(t *testing.T) {
	t.Run("MessageProcessor", func(t *testing.T) {
		processor := &MockMessageProcessor{}
		
		if !processor.CanHandle("test") {
			t.Error("应该能处理test类型")
		}

		if processor.GetPriority() != 1 {
			t.Error("优先级应该为1")
		}

		ctx := context.Background()
		mockEvent := &bot.ImMessageReceiveEvent{
			Message: bot.Message{
				MessageType: "test",
				Content:     "test content",
			},
		}
		result, err := processor.Process(ctx, mockEvent)
		if err != nil {
			t.Errorf("处理失败: %v", err)
		}
		if result == nil {
			t.Error("结果不应该为nil")
		}
	})

	t.Run("ContentRetriever", func(t *testing.T) {
		retriever := &MockContentRetriever{}
		
		if !retriever.CanRetrieve("test", nil) {
			t.Error("应该能检索test类型")
		}

		if retriever.GetType() != "test" {
			t.Error("类型应该为test")
		}

		ctx := context.Background()
		result, err := retriever.Retrieve(ctx, "test_source")
		if err != nil {
			t.Errorf("检索失败: %v", err)
		}
		if result == nil {
			t.Error("结果不应该为nil")
		}
	})
}

// TestRegistries 测试注册表
func TestRegistries(t *testing.T) {
	t.Run("MessageProcessorRegistry", func(t *testing.T) {
		registry := NewMessageProcessorRegistry()
		processor := &MockMessageProcessor{}
		
		registry.Register(processor)
		
		if len(registry.processors) != 1 {
			t.Errorf("处理器数量错误: 期望1, 实际%d", len(registry.processors))
		}

		ctx := context.Background()
		mockEvent := &bot.ImMessageReceiveEvent{
			Message: bot.Message{
				MessageType: "test",
				Content:     "test content",
			},
		}
		result, err := registry.Process(ctx, mockEvent)
		if err != nil {
			t.Errorf("处理失败: %v", err)
		}
		if result == nil {
			t.Error("结果不应该为nil")
		}
	})

	t.Run("ContentRetrieverRegistry", func(t *testing.T) {
		registry := NewContentRetrieverRegistry()
		retriever := &MockContentRetriever{}
		
		registry.Register(retriever)
		
		if len(registry.retrievers) != 1 {
			t.Errorf("检索器数量错误: 期望1, 实际%d", len(registry.retrievers))
		}

		ctx := context.Background()
		result, err := registry.Retrieve(ctx, "test", "test_source")
		if err != nil {
			t.Errorf("检索失败: %v", err)
		}
		if result == nil {
			t.Error("结果不应该为nil")
		}
	})
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	t.Run("ErrorDefinitions", func(t *testing.T) {
		errors := []error{
			ErrNoSuitableProcessor,
			ErrNoSuitableRetriever,
			ErrUnsupportedFormat,
			ErrContentNotFound,
			ErrInvalidSource,
		}

		for i, err := range errors {
			if err == nil {
				t.Errorf("错误 %d 不应该为nil", i)
			}
			if err.Error() == "" {
				t.Errorf("错误 %d 消息不应该为空", i)
			}
		}
	})

	t.Run("NoSuitableProcessor", func(t *testing.T) {
		registry := NewMessageProcessorRegistry()
		ctx := context.Background()
		
		mockEvent := &bot.ImMessageReceiveEvent{
			Message: bot.Message{
				MessageType: "unsupported",
				Content:     "test content",
			},
		}
		_, err := registry.Process(ctx, mockEvent)
		if err != ErrNoSuitableProcessor {
			t.Errorf("应该返回ErrNoSuitableProcessor, 实际: %v", err)
		}
	})

	t.Run("NoSuitableRetriever", func(t *testing.T) {
		registry := NewContentRetrieverRegistry()
		ctx := context.Background()
		
		_, err := registry.Retrieve(ctx, "unsupported", nil)
		if err != ErrNoSuitableRetriever {
			t.Errorf("应该返回ErrNoSuitableRetriever, 实际: %v", err)
		}
	})
}

// Mock implementations for testing

type MockMessageProcessor struct{}

func (p *MockMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "test"
}

func (p *MockMessageProcessor) Process(ctx context.Context, message *bot.ImMessageReceiveEvent) (*ProcessResult, error) {
	return &ProcessResult{
		Content:        "测试处理结果",
		Files:          []*bot.FileData{},
		Metadata:       map[string]interface{}{},
		ShouldContinue: false,
	}, nil
}

func (p *MockMessageProcessor) GetPriority() int {
	return 1
}

type MockContentRetriever struct{}

func (r *MockContentRetriever) CanRetrieve(contentType string, source interface{}) bool {
	return contentType == "test"
}

func (r *MockContentRetriever) Retrieve(ctx context.Context, source interface{}) (*ContentResult, error) {
	return &ContentResult{
		Text:     "测试内容",
		Title:    "测试标题",
		Type:     "test",
		Size:     100,
		Metadata: map[string]interface{}{},
	}, nil
}

func (r *MockContentRetriever) GetType() string {
	return "test"
}



// TestComponentInitialization 测试组件初始化
func TestComponentInitialization(t *testing.T) {
	t.Run("DefaultRegistries", func(t *testing.T) {
		// 测试默认注册表创建
		parsers := &MockContentParserRegistry{}
		retrievers := NewContentRetrieverRegistry()
		processors := &MockMessageProcessorRegistry{}

		if parsers == nil {
			t.Error("解析器注册表不应该为nil")
		}
		if retrievers == nil {
			t.Error("检索器注册表不应该为nil")
		}
		if processors == nil {
			t.Error("处理器注册表不应该为nil")
		}
	})
}

// Mock registries for testing
type MockContentParserRegistry struct{}
type MockMessageProcessorRegistry struct{}

// BenchmarkBasicOperations 基准测试
func BenchmarkBasicOperations(b *testing.B) {
	registry := NewMessageProcessorRegistry()
	processor := &MockMessageProcessor{}
	registry.Register(processor)

	ctx := context.Background()
	message := &bot.ImMessageReceiveEvent{
		Message: bot.Message{
			MessageType: "test",
			Content:     "test content",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := registry.Process(ctx, message)
		if err != nil {
			b.Fatalf("处理失败: %v", err)
		}
	}
}
