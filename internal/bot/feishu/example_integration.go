package feishu

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/svc"
)

// ExampleIntegration 演示如何集成新的飞书机器人到现有系统
func ExampleIntegration() {
	ctx := context.Background()

	// 1. 创建配置（通常从配置文件或环境变量读取）
	cfg := config.Config{
		// 这里需要完整的配置，包括MasterMind、数据库、向量存储等
		MasterMind: config.MasterMind{
			Prompt: "你是一个智能助手，能够帮助用户解决各种问题。",
			LLM:    "openai",
			Model:  "gpt-4",
		},
		SubAgent: config.SubAgent{
			LLM:   "openai",
			Model: "gpt-3.5-turbo",
		},
		// ... 其他配置
	}

	// 2. 创建服务上下文
	svcCtx := svc.NewServiceContext(cfg)

	// 3. 创建飞书机器人配置
	feishuConfig := &FeishuBotConfig{
		AppId:     "cli_your_app_id_here",
		AppSecret: "your_app_secret_here",
		BotName:   "智能助手",
		WebhookURL: "https://your-domain.com/webhook/feishu",
	}
	feishuConfig.DirectiveCard.TemplateId = "ctp_template_id"
	feishuConfig.DirectiveCard.TemplateVersion = "1.0.0"

	// 4. 创建飞书机器人实例
	feishuBot := NewFeishuBot(ctx, feishuConfig, svcCtx)

	// 5. 初始化机器人
	if err := feishuBot.Initialize(ctx); err != nil {
		log.Fatalf("初始化飞书机器人失败: %v", err)
	}

	// 6. 创建机器人管理器并注册
	manager := bot.NewBotManager()
	if err := manager.RegisterBot(bot.PlatformFeishu, feishuBot); err != nil {
		log.Fatalf("注册飞书机器人失败: %v", err)
	}

	// 7. 启动所有机器人
	if err := manager.StartAll(ctx); err != nil {
		log.Fatalf("启动机器人失败: %v", err)
	}

	fmt.Println("飞书机器人集成完成，系统已启动")

	// 8. 演示事件处理
	demonstrateEventHandling(feishuBot)
}

// demonstrateEventHandling 演示事件处理流程
func demonstrateEventHandling(feishuBot *FeishuBot) {
	ctx := context.Background()

	// 模拟接收到的文本消息事件
	textMessageEvent := &bot.Event{
		Type:      "im.message.receive_v1",
		Timestamp: 1640995200000,
		Platform:  bot.PlatformFeishu,
		Data: map[string]interface{}{
			"message": map[string]interface{}{
				"message_id":   "om_test_message_id",
				"message_type": "text",
				"chat_type":    "p2p",
				"chat_id":      "oc_test_chat_id",
				"content":      `{"text":"你好，请帮我分析一下这个文档 https://boke.feishu.cn/wiki/ABC123DEF456"}`,
				"create_time":  "1640995200000",
				"parent_id":    "",
			},
			"sender": map[string]interface{}{
				"sender_id": map[string]interface{}{
					"open_id":  "ou_test_user_id",
					"user_id":  "test_user",
					"union_id": "on_test_union_id",
				},
				"sender_type": "user",
				"tenant_key":  "test_tenant",
			},
		},
	}

	// 处理事件
	if err := feishuBot.HandleEvent(ctx, textMessageEvent); err != nil {
		fmt.Printf("处理文本消息事件失败: %v\n", err)
	} else {
		fmt.Println("文本消息事件处理成功")
	}

	// 模拟P2P加入聊天事件
	joinChatEvent := &bot.Event{
		Type:      "im.chat.member.user.added_v1",
		Timestamp: 1640995200000,
		Platform:  bot.PlatformFeishu,
		Data: map[string]interface{}{
			"operator_id": map[string]interface{}{
				"open_id":  "ou_test_user_id",
				"user_id":  "test_user",
				"union_id": "on_test_union_id",
			},
		},
	}

	// 处理加入聊天事件
	if err := feishuBot.HandleEvent(ctx, joinChatEvent); err != nil {
		fmt.Printf("处理加入聊天事件失败: %v\n", err)
	} else {
		fmt.Println("加入聊天事件处理成功")
	}
}

// ExampleConsumerIntegration 演示如何与现有的消费者框架集成
func ExampleConsumerIntegration() {
	// 这个示例展示了如何将新的飞书机器人集成到现有的NSQ消费者框架中

	// 原有的消费者处理逻辑可以这样重构：
	type ModernConsumeEventHandler struct {
		botManager bot.BotManager
	}

	handler := &ModernConsumeEventHandler{
		botManager: bot.NewBotManager(),
	}

	// 注册飞书机器人到管理器
	ctx := context.Background()
	svcCtx := &svc.ServiceContext{} // 实际使用中需要完整初始化

	feishuConfig := &FeishuBotConfig{
		AppId:     "your_app_id",
		AppSecret: "your_app_secret",
		BotName:   "智能助手",
	}

	feishuBot := NewFeishuBot(ctx, feishuConfig, svcCtx)
	handler.botManager.RegisterBot(bot.PlatformFeishu, feishuBot)

	fmt.Println("现代化消费者框架集成完成")
}

// handleMessage 新的消息处理方法（替代原有的复杂逻辑）
func (h *ModernConsumeEventHandler) handleMessage(eventData []byte) error {
	// 解析事件
	var rawEvent map[string]interface{}
	if err := json.Unmarshal(eventData, &rawEvent); err != nil {
		return fmt.Errorf("解析事件数据失败: %w", err)
	}

	// 构建标准化事件
	event := &bot.Event{
		Type:      rawEvent["type"].(string),
		Data:      rawEvent["data"],
		Timestamp: int64(rawEvent["timestamp"].(float64)),
		Platform:  bot.PlatformFeishu, // 根据实际来源确定
	}

	// 获取对应平台的机器人并处理事件
	botInstance, err := h.botManager.GetBot(bot.PlatformFeishu)
	if err != nil {
		return fmt.Errorf("获取机器人实例失败: %w", err)
	}

	return botInstance.HandleEvent(context.Background(), event)
}

// ExampleMigrationGuide 迁移指南示例
func ExampleMigrationGuide() {
	fmt.Println(`
=== 飞书机器人迁移指南 ===

1. 移除的组件：
   - 直接的命令执行逻辑 (/cmd 处理)
   - 工作流处理逻辑 (processWorkflow)
   - 复杂的指令处理器 (directive.NewCommandProcessor)

2. 新增的功能：
   - MasterMind系统集成
   - 标准化的Bot接口实现
   - 改进的文档内容检索
   - 自然语言对话支持
   - 更好的错误处理和日志记录

3. 迁移步骤：
   a) 更新配置结构，使用新的FeishuBotConfig
   b) 替换直接的消息处理逻辑为Bot接口调用
   c) 配置MasterMind系统参数
   d) 测试新的对话流程

4. 兼容性：
   - 保持与现有NSQ消费者框架的兼容性
   - 支持现有的飞书API调用
   - 维护相同的消息格式和响应结构

5. 性能优化：
   - 使用连接池和缓存
   - 异步处理长时间运行的任务
   - 智能的上下文管理

6. 监控和日志：
   - 结构化日志记录
   - 性能指标收集
   - 错误追踪和报告
`)
}

// ModernConsumeEventHandler 现代化的事件处理器
type ModernConsumeEventHandler struct {
	botManager bot.BotManager
}

// 实现原有的HandleMessage接口以保持兼容性
func (h *ModernConsumeEventHandler) HandleMessage(msg interface{}) error {
	// 这里可以适配原有的nsq.Message或其他消息格式
	// 转换为标准的Bot事件格式并处理
	return h.handleMessage([]byte{}) // 示例实现
}
