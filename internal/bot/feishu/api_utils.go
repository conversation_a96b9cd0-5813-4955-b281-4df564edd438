package feishu

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

// APIClient 封装Lark API客户端，提供统一的错误处理和日志记录
type APIClient struct {
	client *lark.Client
	logger logx.Logger
}

// NewAPIClient 创建API客户端
func NewAPIClient(client *lark.Client, logger logx.Logger) *APIClient {
	return &APIClient{
		client: client,
		logger: logger,
	}
}

// APIResponse 统一的API响应结构
type APIResponse struct {
	Success   bool
	Code      int
	Message   string
	RequestID string
	Data      interface{}
}

// handleAPIResponse 统一处理API响应
func (c *APIClient) handleAPIResponse(resp interface{}, operation string) (*APIResponse, error) {
	var apiResp APIResponse

	switch r := resp.(type) {
	case *larkim.CreateMessageResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      r.Data,
		}
	case *larkim.ReplyMessageResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      r.Data,
		}
	case *larkim.CreateMessageReactionResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      r.Data,
		}
	case *larkim.ListMessageResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      r.Data,
		}
	case *larkim.GetMessageResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      r.Data,
		}
	case *larkwiki.GetNodeSpaceResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      r.Data,
		}
	case *larkdrive.DownloadFileResp:
		apiResp = APIResponse{
			Success:   r.Success(),
			Code:      r.Code,
			Message:   r.Msg,
			RequestID: r.RequestId(),
			Data:      nil, // DownloadFileResp doesn't have Data field
		}
	default:
		return nil, fmt.Errorf("不支持的响应类型: %T", resp)
	}

	// 记录API调用日志
	if apiResp.Success {
		c.logger.Infof("API调用成功: %s, RequestID: %s", operation, apiResp.RequestID)
	} else {
		c.logger.Errorf("API调用失败: %s, Code: %d, Message: %s, RequestID: %s",
			operation, apiResp.Code, apiResp.Message, apiResp.RequestID)
	}

	if !apiResp.Success {
		return &apiResp, fmt.Errorf("API调用失败: %s (Code: %d, RequestID: %s)",
			apiResp.Message, apiResp.Code, apiResp.RequestID)
	}

	return &apiResp, nil
}

// SendTextMessage 发送文本消息
func (c *APIClient) SendTextMessage(ctx context.Context, chatType, chatId, text string) error {
	idType := "open_id"
	if chatType == "group" {
		idType = "chat_id"
	}

	content := struct {
		Text string `json:"text"`
	}{Text: text}

	contentBytes, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化文本内容失败: %w", err)
	}

	req := larkim.NewCreateMessageReqBuilder().ReceiveIdType(idType).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(chatId).
			MsgType("text").
			Content(string(contentBytes)).
			Build()).
		Build()

	resp, err := c.client.Im.V1.Message.Create(ctx, req)
	if err != nil {
		return fmt.Errorf("发送文本消息失败: %w", err)
	}

	_, err = c.handleAPIResponse(resp, "SendTextMessage")
	return err
}

// ReplyMessage 回复消息
func (c *APIClient) ReplyMessage(ctx context.Context, messageId, content, msgType string) error {
	req := larkim.NewReplyMessageReqBuilder().
		MessageId(messageId).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			Content(content).
			MsgType(msgType).
			ReplyInThread(false).
			Build()).
		Build()

	resp, err := c.client.Im.V1.Message.Reply(ctx, req)
	if err != nil {
		return fmt.Errorf("回复消息失败: %w", err)
	}

	_, err = c.handleAPIResponse(resp, "ReplyMessage")
	return err
}

// CreateMessageReaction 创建消息反应
func (c *APIClient) CreateMessageReaction(ctx context.Context, messageId, emoji string) error {
	req := larkim.NewCreateMessageReactionReqBuilder().
		MessageId(messageId).
		Body(larkim.NewCreateMessageReactionReqBodyBuilder().
			ReactionType(larkim.NewEmojiBuilder().
				EmojiType(emoji).
				Build()).
			Build()).
		Build()

	resp, err := c.client.Im.V1.MessageReaction.Create(ctx, req)
	if err != nil {
		return fmt.Errorf("创建消息反应失败: %w", err)
	}

	_, err = c.handleAPIResponse(resp, "CreateMessageReaction")
	return err
}

// ListMessages 获取消息列表
func (c *APIClient) ListMessages(ctx context.Context, chatId, startTime, endTime string, pageSize int) (*larkim.ListMessageResp, error) {
	req := larkim.NewListMessageReqBuilder().
		ContainerIdType("chat").
		ContainerId(chatId).
		StartTime(startTime).
		EndTime(endTime).
		SortType("ByCreateTimeDesc").
		PageSize(pageSize).
		Build()

	resp, err := c.client.Im.V1.Message.List(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", err)
	}

	_, err = c.handleAPIResponse(resp, "ListMessages")
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetMessage 获取单条消息
func (c *APIClient) GetMessage(ctx context.Context, messageId string) (*larkim.GetMessageResp, error) {
	req := larkim.NewGetMessageReqBuilder().
		MessageId(messageId).
		UserIdType("open_id").
		Build()

	resp, err := c.client.Im.V1.Message.Get(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取消息失败: %w", err)
	}

	_, err = c.handleAPIResponse(resp, "GetMessage")
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetWikiNode 获取Wiki节点信息
func (c *APIClient) GetWikiNode(ctx context.Context, token string) (*larkwiki.GetNodeSpaceResp, error) {
	req := larkwiki.NewGetNodeSpaceReqBuilder().
		Token(token).
		Build()

	resp, err := c.client.Wiki.V2.Space.GetNode(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取Wiki节点失败: %w", err)
	}

	_, err = c.handleAPIResponse(resp, "GetWikiNode")
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetWikiNodeContent 获取Wiki节点内容 (使用通用API调用)
func (c *APIClient) GetWikiNodeContent(ctx context.Context, token string) (map[string]interface{}, error) {
	// 使用通用API调用方式
	resp, err := c.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/wiki/v2/nodes/%s", token),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
	})

	if err != nil {
		return nil, fmt.Errorf("获取Wiki节点内容失败: %w", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(resp.RawBody, &result); err != nil {
		return nil, fmt.Errorf("解析Wiki节点内容响应失败: %w", err)
	}

	return result, nil
}

// GetFile 获取文件信息 (使用通用API调用)
func (c *APIClient) GetFile(ctx context.Context, fileToken string) (map[string]interface{}, error) {
	// 使用通用API调用方式
	resp, err := c.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/drive/v1/files/%s", fileToken),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
	})

	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %w", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(resp.RawBody, &result); err != nil {
		return nil, fmt.Errorf("解析文件信息响应失败: %w", err)
	}

	return result, nil
}

// DownloadFile 下载文件内容 - 支持多种下载方式
func (c *APIClient) DownloadFile(ctx context.Context, fileToken string) ([]byte, error) {
	// 首先尝试使用Drive API下载
	req := larkdrive.NewDownloadFileReqBuilder().
		FileToken(fileToken).
		Build()

	resp, err := c.client.Drive.V1.File.Download(ctx, req)
	if err == nil {
		_, err = c.handleAPIResponse(resp, "DownloadFile")
		if err == nil && resp.File != nil {
			if closer, ok := resp.File.(io.Closer); ok {
				defer closer.Close()
			}
			content, readErr := io.ReadAll(resp.File)
			if readErr == nil {
				return content, nil
			}
		}
	}

	// 如果Drive API失败，尝试使用IM文件下载API
	c.logger.Warnf("Drive API下载失败，尝试IM API: %v", err)
	return c.downloadFileViaIM(ctx, fileToken)
}

// downloadFileViaIM 通过IM API下载文件
func (c *APIClient) downloadFileViaIM(ctx context.Context, fileKey string) ([]byte, error) {
	// 使用IM文件下载API
	resp, err := c.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/im/v1/files/%s", fileKey),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant},
	})

	if err != nil {
		return nil, fmt.Errorf("IM API下载文件失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("IM API下载文件失败，HTTP状态码: %d", resp.StatusCode)
	}

	return resp.RawBody, nil
}

// JSONMarshal 统一的JSON序列化，带错误处理
func JSONMarshal(v interface{}) ([]byte, error) {
	data, err := json.Marshal(v)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %w", err)
	}
	return data, nil
}

// JSONUnmarshal 统一的JSON反序列化，带错误处理
func JSONUnmarshal(data []byte, v interface{}) error {
	if err := json.Unmarshal(data, v); err != nil {
		return fmt.Errorf("JSON反序列化失败: %w", err)
	}
	return nil
}
