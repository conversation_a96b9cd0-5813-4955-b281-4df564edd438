package feishu

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	"github.com/run-bigpig/hongdou/internal/pkg/memory"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
	"github.com/run-bigpig/hongdou/internal/service/mastermind"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"log"
	"math/rand"
	"sort"
	"strings"
	"time"
)

const (
	EventTypeImMessageReceive = "im.message.receive_v1"
	EventP2PJoinChat          = "im.chat.access_event.bot_p2p_chat_entered_v1" //用户进入与机器人单聊时
)

// FeishuBot 飞书机器人实现，遵循Bot接口规范
type FeishuBot struct {
	ctx           context.Context
	svcCtx        *svc.ServiceContext
	client        *lark.Client
	apiClient     *APIClient
	logger        logx.Logger
	isInitialized bool
	isHealthy     bool

	// 插件化组件
	messageProcessors *MessageProcessorRegistry
	contentRetrievers *ContentRetrieverRegistry
	contentParsers    *ContentParserRegistry
}

// NewFeishuBot 创建新的飞书机器人实例
func NewFeishuBot(ctx context.Context, svcCtx *svc.ServiceContext) *FeishuBot {
	logger := logx.WithContext(ctx)

	bot := &FeishuBot{
		ctx:    ctx,
		svcCtx: svcCtx,
		logger: logger,
	}

	//初始化机器人
	err := bot.Initialize()
	if err != nil {
		log.Fatalf("初始化机器人失败: %v", err)
	}
	return bot
}

// initializeComponents 初始化插件化组件
func (f *FeishuBot) initializeComponents() {
	// 初始化内容解析器
	f.contentParsers = NewContentParserRegistry()

	// 初始化内容检索器
	f.contentRetrievers = NewContentRetrieverRegistry()

	// 初始化消息处理器
	f.messageProcessors = NewMessageProcessorRegistry()

	// 注册基础处理器
	f.registerBasicProcessors()
}

// registerBasicProcessors 注册基础处理器
func (f *FeishuBot) registerBasicProcessors() {
	// 注册基础文本消息处理器
	textProcessor := &BasicTextMessageProcessor{logger: f.logger}
	f.messageProcessors.Register(textProcessor)

	f.logger.Info("基础消息处理器注册完成")
}

// Initialize 初始化飞书机器人
func (f *FeishuBot) Initialize() error {
	// 初始化Lark客户端
	f.client = lark.NewClient(f.svcCtx.Config.BotConfig.FeishuBot.AppId, f.svcCtx.Config.BotConfig.FeishuBot.AppSecret)
	f.apiClient = NewAPIClient(f.client, f.logger)
	// 初始化插件化组件
	f.initializeComponents()

	f.isInitialized = true
	f.isHealthy = true

	f.logger.Infof("飞书机器人%s初始化成功", f.svcCtx.Config.BotConfig.FeishuBot.BotName)
	return nil
}

// HandleEvent 处理来自飞书的事件
func (f *FeishuBot) HandleEvent(event *Event) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	switch event.Type {
	case EventTypeImMessageReceive:
		return f.handleImMessage(f.ctx, event)
	case EventP2PJoinChat:
		return f.handleP2PJoinChat(f.ctx, event)
	default:
		f.logger.Errorf("未知的事件类型: %s", event.Type)
		return nil
	}
}

// handleImMessage 处理即时消息事件
func (f *FeishuBot) handleImMessage(ctx context.Context, event *Event) error {
	var message ImMessageReceiveEvent
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &message); err != nil {
		return fmt.Errorf("解析即时消息事件失败: %w", err)
	}

	// 检查群聊中是否@了机器人
	if message.Message.ChatType == "group" {
		botMentioned := false
		for _, mention := range message.Message.Mentions {
			if mention.Name == f.svcCtx.Config.BotConfig.FeishuBot.BotName {
				botMentioned = true
				break
			}
		}
		if !botMentioned {
			return nil // 群聊中未@机器人，忽略消息
		}
	}

	switch message.Message.MessageType {
	case "text":
		return f.handleTextMessage(ctx, &message)
	case "file":
		return f.handleFileMessage(ctx, &message)
	case "image":
		return f.handleImageMessage(ctx, &message)
	case "post":
		return f.handleTextMessage(ctx, &message) // 富文本消息使用通用处理器
	default:
		f.logger.Errorf("不支持的消息类型: %s", message.Message.MessageType)
		return nil
	}
}

// handleP2PJoinChat 处理P2P聊天加入事件
func (f *FeishuBot) handleP2PJoinChat(ctx context.Context, event *Event) error {
	var joinEvent P2PJoinChat
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &joinEvent); err != nil {
		return fmt.Errorf("解析P2P加入事件失败: %w", err)
	}

	// 发送欢迎消息
	welcomeMsg := "您好，我是智能助手，有什么可以帮您的吗？"
	return f.SendTextMessage(ctx, "p2p", joinEvent.OperatorId.OpenId, welcomeMsg)
}

// handleTextMessage 处理文本消息（使用插件化架构）
func (f *FeishuBot) handleTextMessage(ctx context.Context, message *ImMessageReceiveEvent) error {
	// 友好的表情回复，表示收到消息
	emojis := []string{"SaluteFace", "OK", "Get", "ThumbsUp"}
	err := f.ReplyEmojiMessage(ctx, message.Message.MessageID, emojis[rand.Intn(len(emojis))])
	if err != nil {
		f.logger.Errorf("回复表情失败: %v", err)
	}

	// 使用消息处理器处理消息
	result, err := f.messageProcessors.Process(ctx, message)
	if err != nil {
		return fmt.Errorf("处理消息失败: %w", err)
	}

	// 通过主脑系统处理
	return f.ProcessWithMasterMind(ctx, message, result)
}

// handleFileMessage 处理文件消息（新增）
func (f *FeishuBot) handleFileMessage(ctx context.Context, message *ImMessageReceiveEvent) error {
	// 使用消息处理器处理文件消息
	result, err := f.messageProcessors.Process(ctx, message)
	if err != nil {
		return fmt.Errorf("处理文件消息失败: %w", err)
	}

	// 通过主脑系统处理
	return f.ProcessWithMasterMind(ctx, message, result)
}

// handleImageMessage 处理图片消息（新增）
func (f *FeishuBot) handleImageMessage(ctx context.Context, message *ImMessageReceiveEvent) error {
	// 使用消息处理器处理图片消息
	result, err := f.messageProcessors.Process(ctx, message)
	if err != nil {
		return fmt.Errorf("处理图片消息失败: %w", err)
	}

	// 通过主脑系统处理
	return f.ProcessWithMasterMind(ctx, message, result)
}

// ProcessWithMasterMind 通过主脑系统处理用户消息（优化版本）
func (f *FeishuBot) ProcessWithMasterMind(ctx context.Context, message *ImMessageReceiveEvent, result *ProcessResult) error {
	// 设置多租户上下文
	ctx = multitenancy.WithOrgID(ctx, "kg_org")
	ctx = multitenancy.WithUserID(ctx, message.Sender.SenderID.OpenID)

	// 设置会话ID（使用聊天ID作为会话标识）
	ctx = memory.WithConversationID(ctx, message.Message.ChatID)

	// 收集上下文信息（使用处理结果）
	contextInfo, err := f.collectContextInfoFromResult(ctx, message, result)
	if err != nil {
		f.logger.Errorf("收集上下文信息失败: %v", err)
		return f.sendFriendlyErrorResponse(ctx, message.Message.MessageID, "抱歉，我在理解您的消息时遇到了一些问题，请稍后再试。")
	}

	// 创建进度通道（用于流式响应）
	progressChan := make(chan types.StreamResponse, 100)
	defer close(progressChan)

	// 启动进度监听协程
	go f.handleProgressUpdates(ctx, message.Message.MessageID, progressChan)

	// 初始化主脑系统
	masterMind := mastermind.NewMastermind(ctx, f.svcCtx, progressChan)

	// 构建增强的用户查询
	enhancedQuery := f.buildEnhancedQueryFromResult(result.Content, contextInfo)

	// 通过主脑系统处理
	response, err := masterMind.Run(enhancedQuery)
	if err != nil {
		f.logger.Errorf("主脑系统处理失败: %v", err)
		return f.sendFriendlyErrorResponse(ctx, message.Message.MessageID, "很抱歉，我现在有点忙不过来，请稍后再试，或者换个方式问我吧～")
	}

	// 发送自然化的回复
	return f.sendNaturalResponse(ctx, message.Message.MessageID, response)
}

// SendTextMessage 发送文本消息（使用API客户端）
func (f *FeishuBot) SendTextMessage(ctx context.Context, chatType, chatId, text string) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	return f.apiClient.SendTextMessage(ctx, chatType, chatId, text)
}

// SendInteractiveMessage 发送交互式消息（如卡片）
func (f *FeishuBot) SendInteractiveMessage(ctx context.Context, messageId string, cardData *CardData) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	replyContent := Card{
		Type: "template",
		Data: *cardData,
	}

	replyContentBytes, err := JSONMarshal(replyContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}

	return f.apiClient.ReplyMessage(ctx, messageId, string(replyContentBytes), "interactive")
}

// ReplyEmojiMessage 回复表情消息（使用API客户端）
func (f *FeishuBot) ReplyEmojiMessage(ctx context.Context, messageId string, emoji string) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	return f.apiClient.CreateMessageReaction(ctx, messageId, emoji)
}

// GetHistoryMessages 获取历史消息（使用API客户端和内容检索器）
func (f *FeishuBot) GetHistoryMessages(ctx context.Context, chatId string, startTime, endTime time.Time, limit int) (*HistoryMessage, error) {
	if !f.isInitialized {
		return nil, errors.New("飞书机器人未初始化")
	}

	resp, err := f.apiClient.ListMessages(ctx, chatId,
		cast.ToString(startTime.Unix()),
		cast.ToString(endTime.Unix()),
		limit)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, errors.New("没有获取到历史消息")
	}

	// 将消息按照创建时间升序排列
	sort.Slice(resp.Data.Items, func(i, j int) bool {
		return cast.ToInt64(resp.Data.Items[i].CreateTime) < cast.ToInt64(resp.Data.Items[j].CreateTime)
	})

	// 提取历史消息
	var texts = make([]*TextData, 0)
	var files = make([]*FileData, 0)

	for _, v := range resp.Data.Items {
		if *(v.Sender.SenderType) == "user" || *(v.Sender.SenderType) == "app" {
			switch *v.MsgType {
			case "file":
				var file FileData
				if err := JSONUnmarshal([]byte(*v.Body.Content), &file); err != nil {
					continue
				}
				file.MessageId = *v.MessageId
				files = append(files, &file)
			case "text":
				var text TextData
				if err := JSONUnmarshal([]byte(*v.Body.Content), &text); err != nil {
					continue
				}

				// 使用URL内容检索器检测Wiki链接
				if contentResult, err := f.contentRetrievers.Retrieve(ctx, "url", text.Text); err == nil {
					file := &FileData{
						FileKey:   text.Text,
						FileName:  contentResult.Title,
						WikiType:  contentResult.Type,
						MessageId: *v.MessageId,
					}
					files = append(files, file)
				} else {
					text.MessageId = *v.MessageId
					texts = append(texts, &text)
				}
			}
		}
	}

	return &HistoryMessage{
		Files: files,
		Texts: texts,
	}, nil
}

// GetMessage 获取指定消息内容（使用API客户端和内容检索器）
func (f *FeishuBot) GetMessage(ctx context.Context, messageId string) (*FileData, string, error) {
	if !f.isInitialized {
		return nil, "", errors.New("飞书机器人未初始化")
	}

	resp, err := f.apiClient.GetMessage(ctx, messageId)
	if err != nil {
		return nil, "", err
	}

	if len(resp.Data.Items) == 0 {
		return nil, "", errors.New("消息不存在")
	}

	message := resp.Data.Items[0]
	switch *message.MsgType {
	case "text":
		var text TextData
		if err := JSONUnmarshal([]byte(*message.Body.Content), &text); err != nil {
			return nil, "", fmt.Errorf("解析文本消息失败: %w", err)
		}

		// 使用URL内容检索器检测Wiki链接
		if contentResult, err := f.contentRetrievers.Retrieve(ctx, "url", text.Text); err == nil {
			file := &FileData{
				FileKey:   text.Text,
				FileName:  contentResult.Title,
				WikiType:  contentResult.Type,
				MessageId: messageId,
			}
			return file, "", nil
		}
		return nil, text.Text, nil
	case "file":
		var file FileData
		if err := JSONUnmarshal([]byte(*message.Body.Content), &file); err != nil {
			return nil, "", fmt.Errorf("解析文件消息失败: %w", err)
		}
		file.MessageId = messageId
		return &file, "", nil
	case "post":
		return nil, *message.Body.Content, nil
	default:
		return nil, "", nil
	}
}

// handleProgressUpdates 处理进度更新
func (f *FeishuBot) handleProgressUpdates(ctx context.Context, messageId string, progressChan <-chan types.StreamResponse) {
	for progress := range progressChan {
		if progress.Type == "progress" && progress.Content != "" {
			// 发送进度更新消息（可选择性实现，避免过多消息）
			f.logger.Debugf("进度更新: %s", progress.Content)
		}
	}
}

// sendFriendlyErrorResponse 发送友好的错误响应（使用API客户端）
func (f *FeishuBot) sendFriendlyErrorResponse(ctx context.Context, messageId, errorMsg string) error {
	content := struct {
		Text string `json:"text"`
	}{Text: errorMsg}

	contentBytes, err := JSONMarshal(content)
	if err != nil {
		return fmt.Errorf("序列化错误消息失败: %w", err)
	}

	return f.apiClient.ReplyMessage(ctx, messageId, string(contentBytes), "text")
}

// sendNaturalResponse 发送自然化的回复（使用API客户端）
func (f *FeishuBot) sendNaturalResponse(ctx context.Context, messageId, response string) error {
	// 确保响应内容不为空
	if strings.TrimSpace(response) == "" {
		response = "抱歉，我暂时无法理解您的问题，请换个方式问我吧～"
	}

	content := struct {
		Text string `json:"text"`
	}{Text: response}

	contentBytes, err := JSONMarshal(content)
	if err != nil {
		return fmt.Errorf("序列化响应内容失败: %w", err)
	}

	return f.apiClient.ReplyMessage(ctx, messageId, string(contentBytes), "text")
}

// collectContextInfoFromResult 从处理结果收集上下文信息
func (f *FeishuBot) collectContextInfoFromResult(ctx context.Context, message *ImMessageReceiveEvent, result *ProcessResult) (*EnhancedContextInfo, error) {
	info := &EnhancedContextInfo{
		Files:           result.Files,
		ProcessMetadata: result.Metadata,
	}

	// 如果有回复消息则提取回复消息内容,否则获取历史消息
	if message.Message.ParentID != "" {
		file, txt, err := f.GetMessage(ctx, message.Message.ParentID)
		if err == nil {
			if file != nil {
				info.Files = append(info.Files, file)
			}
			if txt != "" {
				info.ReplyContext = txt
			}
		}
	} else {
		// 获取历史消息
		endTime := time.Now()
		startTime := endTime.Add(-10 * time.Minute) // 获取10分钟内的消息
		historyMessage, err := f.GetHistoryMessages(ctx, message.Message.ChatID, startTime, endTime, 5)
		if err == nil {
			// 添加历史文件内容
			info.Files = append(info.Files, historyMessage.Files...)
			// 添加历史文本内容
			if len(historyMessage.Texts) > 0 {
				var historyTexts []string
				for _, t := range historyMessage.Texts {
					historyTexts = append(historyTexts, t.Text)
				}
				info.HistoryMessages = strings.Join(historyTexts, "\n")
			}
		}
	}

	// 去重文件
	info.Files = f.deduplicateFiles(info.Files)

	return info, nil
}

// buildEnhancedQueryFromResult 从处理结果构建增强查询
func (f *FeishuBot) buildEnhancedQueryFromResult(text string, contextInfo *EnhancedContextInfo) string {
	var queryParts []string

	// 添加用户原始问题
	queryParts = append(queryParts, fmt.Sprintf("用户问题: %s", text))

	// 添加历史消息上下文
	if contextInfo.HistoryMessages != "" {
		queryParts = append(queryParts, fmt.Sprintf("历史对话: %s", contextInfo.HistoryMessages))
	}

	// 添加回复上下文
	if contextInfo.ReplyContext != "" {
		queryParts = append(queryParts, fmt.Sprintf("回复内容: %s", contextInfo.ReplyContext))
	}

	// 添加文件信息和内容
	if len(contextInfo.Files) > 0 {
		var fileInfos []string
		for _, file := range contextInfo.Files {
			fileInfo := fmt.Sprintf("文件: %s", file.FileName)

			// 如果有文件内容，添加摘要
			if contentResult, ok := contextInfo.ProcessMetadata["file_content"].(*ContentResult); ok {
				fileInfo += fmt.Sprintf(" (内容摘要: %s)", f.truncateText(contentResult.Text, 200))
			}

			fileInfos = append(fileInfos, fileInfo)
		}
		queryParts = append(queryParts, fmt.Sprintf("相关文件: %s", strings.Join(fileInfos, ", ")))
	}

	// 添加Wiki内容
	if wikiContent, ok := contextInfo.ProcessMetadata["wiki_content"].(*ContentResult); ok {
		queryParts = append(queryParts, fmt.Sprintf("Wiki文档: %s (内容: %s)",
			wikiContent.Title, f.truncateText(wikiContent.Text, 300)))
	}

	return strings.Join(queryParts, "\n\n")
}

// deduplicateFiles 去重文件列表
func (f *FeishuBot) deduplicateFiles(files []*FileData) []*FileData {
	existFile := make(map[string]bool)
	newFiles := make([]*FileData, 0)

	for _, file := range files {
		key := file.FileKey
		if key == "" {
			key = file.FileName // 备用键
		}

		if existFile[key] {
			continue
		}
		newFiles = append(newFiles, file)
		existFile[key] = true
	}

	return newFiles
}

// truncateText 截断文本到指定长度
func (f *FeishuBot) truncateText(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}
	return text[:maxLength] + "..."
}

// EnhancedContextInfo 增强的上下文信息结构
type EnhancedContextInfo struct {
	HistoryMessages string
	Files           []*FileData
	ReplyContext    string
	ProcessMetadata map[string]interface{}
}

// BasicTextMessageProcessor 基础文本消息处理器
type BasicTextMessageProcessor struct {
	logger logx.Logger
}

// CanHandle 判断是否能处理该类型的消息
func (p *BasicTextMessageProcessor) CanHandle(messageType string) bool {
	return messageType == "text"
}

// Process 处理文本消息
func (p *BasicTextMessageProcessor) Process(ctx context.Context, message *ImMessageReceiveEvent) (*ProcessResult, error) {
	var content struct {
		Text string `json:"text"`
	}

	if err := json.Unmarshal([]byte(message.Message.Content), &content); err != nil {
		return nil, fmt.Errorf("解析文本消息内容失败: %w", err)
	}

	// 清理文本（移除@用户标记）
	cleanText := p.cleanText(content.Text)

	return &ProcessResult{
		Content:        cleanText,
		Files:          []*FileData{},
		Metadata:       map[string]interface{}{},
		ShouldContinue: false,
	}, nil
}

// GetPriority 获取处理器优先级
func (p *BasicTextMessageProcessor) GetPriority() int {
	return 10 // 中等优先级
}

// cleanText 清理文本内容
func (p *BasicTextMessageProcessor) cleanText(text string) string {
	// 移除@用户标记的简单实现
	if strings.Contains(text, "@_user_") {
		// 简单的清理逻辑
		parts := strings.Split(text, " ")
		var cleaned []string
		for _, part := range parts {
			if !strings.HasPrefix(part, "@_user_") {
				cleaned = append(cleaned, part)
			}
		}
		return strings.TrimSpace(strings.Join(cleaned, " "))
	}
	return strings.TrimSpace(text)
}
