package feishu

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zeromicro/go-zero/core/logx"
)

// TestFeishuBotIntegration 集成测试
func TestFeishuBotIntegration(t *testing.T) {
	// 跳过集成测试，除非设置了环境变量
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	ctx := context.Background()
	
	// 创建测试配置
	config := &FeishuBotConfig{
		AppId:     "cli_test_app_id",
		AppSecret: "test_app_secret",
		BotName:   "测试机器人",
	}

	// 创建模拟的服务上下文
	svcCtx := &svc.ServiceContext{}

	// 创建机器人实例
	bot := NewFeishuBot(ctx, config, svcCtx)
	
	// 验证初始化
	assert.NotNil(t, bot)
	assert.Equal(t, config, bot.config)
	assert.NotNil(t, bot.messageProcessors)
	assert.NotNil(t, bot.contentRetrievers)
	assert.NotNil(t, bot.contentParsers)
}

// TestContentRetrievers 测试内容检索器
func TestContentRetrievers(t *testing.T) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)

	// 创建模拟API客户端
	mockAPIClient := &MockAPIClient{}
	
	// 创建内容解析器注册表
	parsers := DefaultContentParserRegistry(logger)
	
	// 创建内容检索器注册表
	retrievers := NewContentRetrieverRegistry()
	
	// 注册检索器
	fileRetriever := NewFileContentRetriever(mockAPIClient, parsers, logger)
	wikiRetriever := NewWikiContentRetriever(mockAPIClient, logger)
	urlRetriever := NewURLContentRetriever(wikiRetriever, logger)
	
	retrievers.Register(fileRetriever)
	retrievers.Register(wikiRetriever)
	retrievers.Register(urlRetriever)

	t.Run("文件内容检索", func(t *testing.T) {
		fileData := &bot.FileData{
			FileKey:  "test_file_key",
			FileName: "test.txt",
			FileType: "txt",
		}

		// 设置模拟响应
		mockAPIClient.SetFileContent("test_file_key", []byte("这是测试文件内容"))

		result, err := retrievers.Retrieve(ctx, "file", fileData)
		if err != nil {
			t.Logf("文件检索失败（预期，因为是模拟环境）: %v", err)
		} else {
			assert.NotNil(t, result)
			assert.Equal(t, "test.txt", result.Title)
		}
	})

	t.Run("Wiki内容检索", func(t *testing.T) {
		wikiUrl := "https://boke.feishu.cn/wiki/test123"

		result, err := retrievers.Retrieve(ctx, "wiki", wikiUrl)
		if err != nil {
			t.Logf("Wiki检索失败（预期，因为是模拟环境）: %v", err)
		} else {
			assert.NotNil(t, result)
		}
	})

	t.Run("URL内容检索", func(t *testing.T) {
		textWithWiki := "请查看文档 https://boke.feishu.cn/wiki/ABC123"

		result, err := retrievers.Retrieve(ctx, "url", textWithWiki)
		if err != nil {
			t.Logf("URL检索失败（预期，因为是模拟环境）: %v", err)
		} else {
			assert.NotNil(t, result)
		}
	})
}

// TestMessageProcessors 测试消息处理器
func TestMessageProcessors(t *testing.T) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)

	// 创建内容检索器注册表
	retrievers := NewContentRetrieverRegistry()
	
	// 创建消息处理器注册表
	processors := DefaultMessageProcessorRegistry(retrievers, logger)

	t.Run("文本消息处理", func(t *testing.T) {
		message := &bot.ImMessageReceiveEvent{
			Message: bot.Message{
				MessageID:   "test_msg_1",
				MessageType: "text",
				Content:     `{"text":"你好，请帮我分析文档"}`,
			},
		}

		result, err := processors.Process(ctx, message)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "你好，请帮我分析文档", result.Content)
	})

	t.Run("文件消息处理", func(t *testing.T) {
		message := &bot.ImMessageReceiveEvent{
			Message: bot.Message{
				MessageID:   "test_msg_2",
				MessageType: "file",
				Content:     `{"file_key":"test_key","file_name":"test.docx","file_type":"docx"}`,
			},
		}

		result, err := processors.Process(ctx, message)
		if err != nil {
			t.Logf("文件消息处理失败（预期，因为是模拟环境）: %v", err)
		} else {
			assert.NotNil(t, result)
			assert.Contains(t, result.Content, "test.docx")
		}
	})

	t.Run("图片消息处理", func(t *testing.T) {
		message := &bot.ImMessageReceiveEvent{
			Message: bot.Message{
				MessageID:   "test_msg_3",
				MessageType: "image",
				Content:     `{"image_key":"test_image_key","width":800,"height":600}`,
			},
		}

		result, err := processors.Process(ctx, message)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Contains(t, result.Content, "图片")
		assert.Contains(t, result.Content, "800x600")
	})
}

// TestBotInterface 测试Bot接口实现
func TestBotInterface(t *testing.T) {
	ctx := context.Background()
	
	config := &FeishuBotConfig{
		AppId:     "cli_test_app_id",
		AppSecret: "test_app_secret",
		BotName:   "测试机器人",
	}

	svcCtx := &svc.ServiceContext{}
	bot := NewFeishuBot(ctx, config, svcCtx)

	// 验证Bot接口实现
	var _ bot.Bot = bot

	t.Run("GetPlatform", func(t *testing.T) {
		platform := bot.GetPlatform()
		assert.Equal(t, bot.BotPlatform("feishu"), platform)
	})

	t.Run("IsHealthy", func(t *testing.T) {
		// 初始状态应该不健康
		assert.False(t, bot.IsHealthy())
	})

	t.Run("Cleanup", func(t *testing.T) {
		err := bot.Cleanup()
		assert.NoError(t, err)
		assert.False(t, bot.IsHealthy())
	})
}

// TestEventHandling 测试事件处理
func TestEventHandling(t *testing.T) {
	ctx := context.Background()
	
	config := &FeishuBotConfig{
		AppId:     "cli_test_app_id",
		AppSecret: "test_app_secret",
		BotName:   "测试机器人",
	}

	svcCtx := &svc.ServiceContext{}
	bot := NewFeishuBot(ctx, config, svcCtx)

	t.Run("处理文本消息事件", func(t *testing.T) {
		event := &bot.Event{
			Type:      "im.message.receive_v1",
			Timestamp: time.Now().Unix(),
			Platform:  bot.PlatformFeishu,
			Data: map[string]interface{}{
				"message": map[string]interface{}{
					"message_id":   "test_msg_id",
					"message_type": "text",
					"chat_type":    "p2p",
					"chat_id":      "test_chat_id",
					"content":      `{"text":"你好"}`,
					"create_time":  "1640995200000",
				},
				"sender": map[string]interface{}{
					"sender_id": map[string]interface{}{
						"open_id": "test_user_id",
					},
					"sender_type": "user",
				},
			},
		}

		err := bot.HandleEvent(ctx, event)
		if err != nil {
			t.Logf("事件处理失败（预期，因为未初始化）: %v", err)
		}
	})

	t.Run("处理P2P加入事件", func(t *testing.T) {
		event := &bot.Event{
			Type:      "im.chat.member.user.added_v1",
			Timestamp: time.Now().Unix(),
			Platform:  bot.PlatformFeishu,
			Data: map[string]interface{}{
				"operator_id": map[string]interface{}{
					"open_id": "test_user_id",
				},
			},
		}

		err := bot.HandleEvent(ctx, event)
		if err != nil {
			t.Logf("事件处理失败（预期，因为未初始化）: %v", err)
		}
	})
}

// MockAPIClient 模拟API客户端
type MockAPIClient struct {
	fileContents map[string][]byte
}

func (m *MockAPIClient) SetFileContent(fileKey string, content []byte) {
	if m.fileContents == nil {
		m.fileContents = make(map[string][]byte)
	}
	m.fileContents[fileKey] = content
}

// TestContentParsers 测试内容解析器
func TestContentParsers(t *testing.T) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)

	parsers := DefaultContentParserRegistry(logger)

	t.Run("文本解析器", func(t *testing.T) {
		content := []byte("这是一个测试文档\n包含多行内容\nhttps://example.com")
		
		result, err := parsers.Parse(ctx, content, "txt")
		require.NoError(t, err)
		assert.Equal(t, string(content), result.Text)
		assert.Len(t, result.Links, 1)
		assert.Equal(t, "https://example.com", result.Links[0].URL)
	})

	t.Run("不支持的格式", func(t *testing.T) {
		content := []byte("test content")
		
		_, err := parsers.Parse(ctx, content, "unsupported")
		assert.Error(t, err)
		assert.Equal(t, ErrUnsupportedFormat, err)
	})
}

// BenchmarkContentRetrieval 性能基准测试
func BenchmarkContentRetrieval(b *testing.B) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)

	mockAPIClient := &MockAPIClient{}
	parsers := DefaultContentParserRegistry(logger)
	retriever := NewFileContentRetriever(mockAPIClient, parsers, logger)

	// 设置测试数据
	testContent := []byte("这是一个测试文档，包含大量文本内容用于性能测试。")
	mockAPIClient.SetFileContent("benchmark_file", testContent)

	fileData := &bot.FileData{
		FileKey:  "benchmark_file",
		FileName: "benchmark.txt",
		FileType: "txt",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := retriever.Retrieve(ctx, fileData)
		if err != nil {
			b.Logf("检索失败: %v", err)
		}
	}
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	ctx := context.Background()
	logger := logx.WithContext(ctx)

	t.Run("无效源类型", func(t *testing.T) {
		mockAPIClient := &MockAPIClient{}
		parsers := DefaultContentParserRegistry(logger)
		retriever := NewFileContentRetriever(mockAPIClient, parsers, logger)

		_, err := retriever.Retrieve(ctx, "invalid_source")
		assert.Error(t, err)
		assert.Equal(t, ErrInvalidSource, err)
	})

	t.Run("不支持的内容类型", func(t *testing.T) {
		retrievers := NewContentRetrieverRegistry()
		
		_, err := retrievers.Retrieve(ctx, "unsupported_type", nil)
		assert.Error(t, err)
		assert.Equal(t, ErrNoSuitableRetriever, err)
	})
}
