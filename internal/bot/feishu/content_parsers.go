package feishu

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"regexp"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

// TextContentParser 纯文本内容解析器
type TextContentParser struct {
	logger logx.Logger
}

// NewTextContentParser 创建纯文本内容解析器
func NewTextContentParser(logger logx.Logger) *TextContentParser {
	return &TextContentParser{logger: logger}
}

// SupportedFormats 返回支持的文件格式
func (p *TextContentParser) SupportedFormats() []string {
	return []string{"txt", "md", "text"}
}

// Parse 解析纯文本内容
func (p *TextContentParser) Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error) {
	text := string(content)

	// 提取链接
	links := p.extractLinks(text)

	return &ParsedContent{
		Text:      text,
		Structure: map[string]interface{}{"type": "text", "format": format},
		Links:     links,
	}, nil
}

// extractLinks 提取文本中的链接
func (p *TextContentParser) extractLinks(text string) []LinkInfo {
	var links []LinkInfo

	// 匹配HTTP/HTTPS链接
	re := regexp.MustCompile(`https?://[^\s<>"{}|\\^` + "`" + `\[\]]+`)
	matches := re.FindAllString(text, -1)

	for _, match := range matches {
		links = append(links, LinkInfo{
			URL:  match,
			Text: match,
		})
	}

	return links
}

// DocxContentParser DOCX文档内容解析器
type DocxContentParser struct {
	logger logx.Logger
}

// NewDocxContentParser 创建DOCX内容解析器
func NewDocxContentParser(logger logx.Logger) *DocxContentParser {
	return &DocxContentParser{logger: logger}
}

// SupportedFormats 返回支持的文件格式
func (p *DocxContentParser) SupportedFormats() []string {
	return []string{"docx"}
}

// Parse 解析DOCX文档内容
func (p *DocxContentParser) Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error) {
	reader, err := zip.NewReader(bytes.NewReader(content), int64(len(content)))
	if err != nil {
		return nil, fmt.Errorf("打开DOCX文件失败: %w", err)
	}

	var documentText strings.Builder
	var images []ImageInfo
	var links []LinkInfo
	var tables []TableData

	// 查找document.xml文件
	for _, file := range reader.File {
		switch file.Name {
		case "word/document.xml":
			text, err := p.parseDocumentXML(file)
			if err != nil {
				p.logger.Errorf("解析document.xml失败: %v", err)
				continue
			}
			documentText.WriteString(text)

		case "word/_rels/document.xml.rels":
			// 解析关系文件，获取链接和图片信息
			fileLinks, fileImages := p.parseRelationships(file)
			links = append(links, fileLinks...)
			images = append(images, fileImages...)
		}
	}

	return &ParsedContent{
		Text:   documentText.String(),
		Images: images,
		Links:  links,
		Tables: tables,
		Structure: map[string]interface{}{
			"type":   "document",
			"format": "docx",
		},
	}, nil
}

// parseDocumentXML 解析document.xml文件
func (p *DocxContentParser) parseDocumentXML(file *zip.File) (string, error) {
	rc, err := file.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()

	content, err := io.ReadAll(rc)
	if err != nil {
		return "", err
	}

	// 简单的XML文本提取（实际实现中可能需要更复杂的XML解析）
	text := string(content)

	// 移除XML标签，提取文本内容
	re := regexp.MustCompile(`<w:t[^>]*>([^<]*)</w:t>`)
	matches := re.FindAllStringSubmatch(text, -1)

	var result strings.Builder
	for _, match := range matches {
		if len(match) > 1 {
			result.WriteString(match[1])
			result.WriteString(" ")
		}
	}

	return result.String(), nil
}

// parseRelationships 解析关系文件
func (p *DocxContentParser) parseRelationships(file *zip.File) ([]LinkInfo, []ImageInfo) {
	var links []LinkInfo
	var images []ImageInfo

	rc, err := file.Open()
	if err != nil {
		return links, images
	}
	defer rc.Close()

	content, err := io.ReadAll(rc)
	if err != nil {
		return links, images
	}

	// 解析关系XML（简化实现）
	type Relationship struct {
		ID     string `xml:"Id,attr"`
		Type   string `xml:"Type,attr"`
		Target string `xml:"Target,attr"`
	}

	type Relationships struct {
		Relationships []Relationship `xml:"Relationship"`
	}

	var rels Relationships
	if err := xml.Unmarshal(content, &rels); err != nil {
		p.logger.Errorf("解析关系XML失败: %v", err)
		return links, images
	}

	for _, rel := range rels.Relationships {
		if strings.Contains(rel.Type, "hyperlink") {
			links = append(links, LinkInfo{
				URL:  rel.Target,
				Text: rel.Target,
			})
		} else if strings.Contains(rel.Type, "image") {
			images = append(images, ImageInfo{
				URL: rel.Target,
			})
		}
	}

	return links, images
}

// XlsxContentParser XLSX表格内容解析器
type XlsxContentParser struct {
	logger logx.Logger
}

// NewXlsxContentParser 创建XLSX内容解析器
func NewXlsxContentParser(logger logx.Logger) *XlsxContentParser {
	return &XlsxContentParser{logger: logger}
}

// SupportedFormats 返回支持的文件格式
func (p *XlsxContentParser) SupportedFormats() []string {
	return []string{"xlsx"}
}

// Parse 解析XLSX表格内容
func (p *XlsxContentParser) Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error) {
	reader, err := zip.NewReader(bytes.NewReader(content), int64(len(content)))
	if err != nil {
		return nil, fmt.Errorf("打开XLSX文件失败: %w", err)
	}

	var tables []TableData
	var textContent strings.Builder

	// 查找工作表文件
	for _, file := range reader.File {
		if strings.HasPrefix(file.Name, "xl/worksheets/sheet") && strings.HasSuffix(file.Name, ".xml") {
			table, err := p.parseWorksheet(file)
			if err != nil {
				p.logger.Errorf("解析工作表失败: %v", err)
				continue
			}
			tables = append(tables, table)

			// 将表格内容转换为文本
			textContent.WriteString(p.tableToText(table))
			textContent.WriteString("\n\n")
		}
	}

	return &ParsedContent{
		Text:   textContent.String(),
		Tables: tables,
		Structure: map[string]interface{}{
			"type":   "spreadsheet",
			"format": "xlsx",
			"sheets": len(tables),
		},
	}, nil
}

// parseWorksheet 解析工作表
func (p *XlsxContentParser) parseWorksheet(file *zip.File) (TableData, error) {
	rc, err := file.Open()
	if err != nil {
		return TableData{}, err
	}
	defer rc.Close()

	content, err := io.ReadAll(rc)
	if err != nil {
		return TableData{}, err
	}

	// 简化的表格解析（实际实现需要更复杂的XML解析和共享字符串处理）
	text := string(content)

	// 提取单元格值（这是一个简化的实现）
	re := regexp.MustCompile(`<v>([^<]*)</v>`)
	matches := re.FindAllStringSubmatch(text, -1)

	var values []string
	for _, match := range matches {
		if len(match) > 1 {
			values = append(values, match[1])
		}
	}

	// 简单地将值组织成行（实际实现需要考虑单元格位置）
	const maxCols = 10 // 假设最大10列
	var rows [][]string
	for i := 0; i < len(values); i += maxCols {
		end := i + maxCols
		if end > len(values) {
			end = len(values)
		}
		rows = append(rows, values[i:end])
	}

	var headers []string
	if len(rows) > 0 {
		headers = rows[0]
		rows = rows[1:]
	}

	return TableData{
		Headers: headers,
		Rows:    rows,
		Caption: fmt.Sprintf("工作表 (%s)", file.Name),
	}, nil
}

// tableToText 将表格转换为文本
func (p *XlsxContentParser) tableToText(table TableData) string {
	var result strings.Builder

	if table.Caption != "" {
		result.WriteString(fmt.Sprintf("表格: %s\n", table.Caption))
	}

	// 写入表头
	if len(table.Headers) > 0 {
		result.WriteString(strings.Join(table.Headers, " | "))
		result.WriteString("\n")
		result.WriteString(strings.Repeat("-", len(strings.Join(table.Headers, " | "))))
		result.WriteString("\n")
	}

	// 写入数据行
	for _, row := range table.Rows {
		result.WriteString(strings.Join(row, " | "))
		result.WriteString("\n")
	}

	return result.String()
}

// PDFContentParser PDF内容解析器（基础实现）
type PDFContentParser struct {
	logger logx.Logger
}

// NewPDFContentParser 创建PDF内容解析器
func NewPDFContentParser(logger logx.Logger) *PDFContentParser {
	return &PDFContentParser{logger: logger}
}

// SupportedFormats 返回支持的文件格式
func (p *PDFContentParser) SupportedFormats() []string {
	return []string{"pdf"}
}

// Parse 解析PDF内容
func (p *PDFContentParser) Parse(ctx context.Context, content []byte, format string) (*ParsedContent, error) {
	// PDF解析需要专门的库，这里提供一个基础实现
	// 实际使用中可以集成如 github.com/ledongthuc/pdf 等库

	return &ParsedContent{
		Text: fmt.Sprintf("PDF文档 (大小: %d bytes) - 需要专门的PDF解析库来提取内容", len(content)),
		Structure: map[string]interface{}{
			"type":   "pdf",
			"format": "pdf",
			"size":   len(content),
		},
	}, nil
}

// DefaultContentParserRegistry 创建默认的内容解析器注册表
func DefaultContentParserRegistry(logger logx.Logger) *ContentParserRegistry {
	registry := NewContentParserRegistry()

	// 注册文本解析器
	textParser := NewTextContentParser(logger)
	for _, format := range textParser.SupportedFormats() {
		registry.Register(format, textParser)
	}

	// 注册DOCX解析器
	docxParser := NewDocxContentParser(logger)
	for _, format := range docxParser.SupportedFormats() {
		registry.Register(format, docxParser)
	}

	// 注册XLSX解析器
	xlsxParser := NewXlsxContentParser(logger)
	for _, format := range xlsxParser.SupportedFormats() {
		registry.Register(format, xlsxParser)
	}

	// 注册PDF解析器
	pdfParser := NewPDFContentParser(logger)
	for _, format := range pdfParser.SupportedFormats() {
		registry.Register(format, pdfParser)
	}

	return registry
}
