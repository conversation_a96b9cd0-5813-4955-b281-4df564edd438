package feishu

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/zeromicro/go-zero/core/logx"
)

// FileContentRetriever 文件内容检索器 - 基于官方API文档实现
type FileContentRetriever struct {
	apiClient *APIClient
	parser    *ContentParserRegistry
	logger    logx.Logger
}

// NewFileContentRetriever 创建文件内容检索器
func NewFileContentRetriever(apiClient *APIClient, parser *ContentParserRegistry, logger logx.Logger) *FileContentRetriever {
	return &FileContentRetriever{
		apiClient: apiClient,
		parser:    parser,
		logger:    logger,
	}
}

// CanRetrieve 判断是否能检索该类型的内容
func (r *FileContentRetriever) CanRetrieve(contentType string, source interface{}) bool {
	return contentType == "file" || contentType == "document"
}

// Retrieve 检索文件内容 - 基于官方Lark Drive API实现
func (r *FileContentRetriever) Retrieve(ctx context.Context, source interface{}) (*ContentResult, error) {
	fileData, ok := source.(*bot.FileData)
	if !ok {
		return nil, ErrInvalidSource
	}

	// 首先获取文件元数据 - 使用官方API
	metaResult, err := r.getFileMetadata(ctx, fileData.FileKey)
	if err != nil {
		r.logger.Errorf("获取文件元数据失败: %v", err)
		// 继续尝试直接下载
	}

	// 尝试直接下载文件内容 - 基于官方下载API
	content, err := r.downloadFileContent(ctx, fileData.FileKey)
	if err != nil {
		return nil, fmt.Errorf("下载文件内容失败: %w", err)
	}

	// 确定文件格式
	var format, fileName, mimeType string
	var createdTime, modifiedTime interface{}

	if metaResult != nil {
		fileName = metaResult.Title
		format = r.determineFileFormatFromMeta(fileName, metaResult.Type)
		createdTime = metaResult.CreateTime
		modifiedTime = metaResult.LatestModifyTime
	} else {
		fileName = fileData.FileName
		format = r.determineFileFormat(fileName, "")
	}

	// 解析文件内容
	parsedContent, err := r.parser.Parse(ctx, content, format)
	if err != nil {
		r.logger.Errorf("解析文件内容失败，返回原始内容: %v", err)
		// 如果解析失败，返回基本信息
		return &ContentResult{
			Text:       fmt.Sprintf("文件: %s (大小: %d bytes)", fileName, len(content)),
			Title:      fileName,
			Type:       format,
			Size:       int64(len(content)),
			RawContent: content,
			Metadata: map[string]interface{}{
				"mime_type":   mimeType,
				"created_at":  createdTime,
				"modified_at": modifiedTime,
				"file_key":    fileData.FileKey,
				"parse_error": err.Error(),
			},
		}, nil
	}

	return &ContentResult{
		Text:       parsedContent.Text,
		Title:      fileName,
		Type:       format,
		Size:       int64(len(content)),
		RawContent: content,
		Metadata: map[string]interface{}{
			"mime_type":   mimeType,
			"created_at":  createdTime,
			"modified_at": modifiedTime,
			"file_key":    fileData.FileKey,
			"structure":   parsedContent.Structure,
			"images":      parsedContent.Images,
			"tables":      parsedContent.Tables,
			"links":       parsedContent.Links,
		},
	}, nil
}

// GetType 获取检索器类型
func (r *FileContentRetriever) GetType() string {
	return "file"
}

// getFileMetadata 获取文件元数据 - 基于官方API
func (r *FileContentRetriever) getFileMetadata(ctx context.Context, fileToken string) (*FileMetadata, error) {
	// 使用简化的API调用，避免复杂的请求体构建
	resp, err := r.apiClient.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/drive/v1/files/%s", fileToken),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
	})

	if err != nil {
		return nil, fmt.Errorf("调用文件元数据API失败: %w", err)
	}

	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Token        string `json:"token"`
			Name         string `json:"name"`
			Type         string `json:"type"`
			ParentToken  string `json:"parent_token"`
			OwnerId      string `json:"owner_id"`
			CreatedTime  string `json:"created_time"`
			ModifiedTime string `json:"modified_time"`
			Size         int64  `json:"size"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.RawBody, &result); err != nil {
		return nil, fmt.Errorf("解析元数据响应失败: %w", err)
	}

	if result.Code != 0 {
		return nil, fmt.Errorf("获取文件元数据失败: %s (code: %d)", result.Msg, result.Code)
	}

	return &FileMetadata{
		DocToken:         result.Data.Token,
		Type:             result.Data.Type,
		Title:            result.Data.Name,
		OwnerID:          result.Data.OwnerId,
		CreateTime:       result.Data.CreatedTime,
		LatestModifyTime: result.Data.ModifiedTime,
		URL:              "",
	}, nil
}

// downloadFileContent 下载文件内容 - 基于官方下载API
func (r *FileContentRetriever) downloadFileContent(ctx context.Context, fileKey string) ([]byte, error) {
	// 使用APIClient的下载方法
	return r.apiClient.DownloadFile(ctx, fileKey)
}

// determineFileFormat 根据文件名和MIME类型确定文件格式
func (r *FileContentRetriever) determineFileFormat(fileName, mimeType string) string {
	// 优先根据MIME类型判断
	switch mimeType {
	case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
		return "docx"
	case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
		return "xlsx"
	case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
		return "pptx"
	case "application/pdf":
		return "pdf"
	case "text/plain":
		return "txt"
	case "text/markdown":
		return "md"
	}

	// 根据文件扩展名判断
	if idx := strings.LastIndex(fileName, "."); idx != -1 {
		ext := strings.ToLower(fileName[idx+1:])
		switch ext {
		case "docx", "xlsx", "pptx", "pdf", "txt", "md":
			return ext
		}
	}

	return "unknown"
}

// determineFileFormatFromMeta 从元数据确定文件格式
func (r *FileContentRetriever) determineFileFormatFromMeta(fileName, docType string) string {
	// 根据文档类型映射
	switch docType {
	case "doc", "docx":
		return "docx"
	case "sheet", "sheets":
		return "xlsx"
	case "slide":
		return "pptx"
	case "file":
		return r.determineFileFormat(fileName, "")
	default:
		return r.determineFileFormat(fileName, "")
	}
}

// FileMetadata 文件元数据结构
type FileMetadata struct {
	DocToken         string `json:"doc_token"`
	Type             string `json:"doc_type"`
	Title            string `json:"title"`
	OwnerID          string `json:"owner_id"`
	CreateTime       string `json:"create_time"`
	LatestModifyUser string `json:"latest_modify_user"`
	LatestModifyTime string `json:"latest_modify_time"`
	URL              string `json:"url"`
}

// WikiContentRetriever Wiki内容检索器 - 基于官方Wiki API实现
type WikiContentRetriever struct {
	apiClient *APIClient
	logger    logx.Logger
}

// NewWikiContentRetriever 创建Wiki内容检索器
func NewWikiContentRetriever(apiClient *APIClient, logger logx.Logger) *WikiContentRetriever {
	return &WikiContentRetriever{
		apiClient: apiClient,
		logger:    logger,
	}
}

// CanRetrieve 判断是否能检索该类型的内容
func (r *WikiContentRetriever) CanRetrieve(contentType string, source interface{}) bool {
	return contentType == "wiki" || contentType == "knowledge_base"
}

// Retrieve 检索Wiki内容 - 基于官方Wiki API实现
func (r *WikiContentRetriever) Retrieve(ctx context.Context, source interface{}) (*ContentResult, error) {
	var token string

	switch s := source.(type) {
	case string:
		// 如果是URL，提取token
		if strings.Contains(s, "https://boke.feishu.cn/wiki/") {
			token = r.extractWikiToken(s)
		} else {
			token = s
		}
	case *bot.FileData:
		if s.WikiType != "" {
			token = s.FileKey
		}
	default:
		return nil, ErrInvalidSource
	}

	if token == "" {
		return nil, fmt.Errorf("无效的Wiki token")
	}

	// 获取Wiki节点信息 - 使用官方API
	nodeInfo, err := r.getWikiNodeInfo(ctx, token)
	if err != nil {
		return nil, fmt.Errorf("获取Wiki节点信息失败: %w", err)
	}

	// 根据obj_type获取实际文档内容
	actualContent, err := r.getActualDocumentContent(ctx, nodeInfo.ObjToken, nodeInfo.ObjType)
	if err != nil {
		r.logger.Errorf("获取实际文档内容失败，使用基本信息: %v", err)
		// 如果获取详细内容失败，返回基本信息
		return &ContentResult{
			Text:  fmt.Sprintf("Wiki文档: %s", nodeInfo.Title),
			Title: nodeInfo.Title,
			Type:  nodeInfo.ObjType,
			Metadata: map[string]interface{}{
				"node_token":    token,
				"obj_token":     nodeInfo.ObjToken,
				"obj_type":      nodeInfo.ObjType,
				"space_id":      nodeInfo.SpaceID,
				"created_time":  nodeInfo.ObjCreateTime,
				"modified_time": nodeInfo.ObjEditTime,
			},
		}, nil
	}

	return &ContentResult{
		Text:  actualContent,
		Title: nodeInfo.Title,
		Type:  nodeInfo.ObjType,
		Metadata: map[string]interface{}{
			"node_token":    token,
			"obj_token":     nodeInfo.ObjToken,
			"obj_type":      nodeInfo.ObjType,
			"space_id":      nodeInfo.SpaceID,
			"created_time":  nodeInfo.ObjCreateTime,
			"modified_time": nodeInfo.ObjEditTime,
		},
	}, nil
}

// GetType 获取检索器类型
func (r *WikiContentRetriever) GetType() string {
	return "wiki"
}

// getWikiNodeInfo 获取Wiki节点信息 - 基于官方API
func (r *WikiContentRetriever) getWikiNodeInfo(ctx context.Context, nodeToken string) (*WikiNodeInfo, error) {
	// 使用官方Wiki API获取节点信息
	resp, err := r.apiClient.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/wiki/v2/spaces/get_node?token=%s", nodeToken),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
	})

	if err != nil {
		return nil, fmt.Errorf("调用Wiki节点API失败: %w", err)
	}

	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Node struct {
				SpaceID         string `json:"space_id"`
				NodeToken       string `json:"node_token"`
				ObjToken        string `json:"obj_token"`
				ObjType         string `json:"obj_type"`
				ParentToken     string `json:"parent_node_token"`
				NodeType        string `json:"node_type"`
				OriginNodeToken string `json:"origin_node_token"`
				OriginSpaceID   string `json:"origin_space_id"`
				HasChild        bool   `json:"has_child"`
				Title           string `json:"title"`
				ObjCreateTime   string `json:"obj_create_time"`
				ObjEditTime     string `json:"obj_edit_time"`
				NodeCreateTime  string `json:"node_create_time"`
			} `json:"node"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.RawBody, &result); err != nil {
		return nil, fmt.Errorf("解析Wiki节点响应失败: %w", err)
	}

	if result.Code != 0 {
		return nil, fmt.Errorf("获取Wiki节点失败: %s (code: %d)", result.Msg, result.Code)
	}

	node := result.Data.Node
	return &WikiNodeInfo{
		SpaceID:         node.SpaceID,
		NodeToken:       node.NodeToken,
		ObjToken:        node.ObjToken,
		ObjType:         node.ObjType,
		ParentToken:     node.ParentToken,
		NodeType:        node.NodeType,
		OriginNodeToken: node.OriginNodeToken,
		OriginSpaceID:   node.OriginSpaceID,
		HasChild:        node.HasChild,
		Title:           node.Title,
		ObjCreateTime:   node.ObjCreateTime,
		ObjEditTime:     node.ObjEditTime,
		NodeCreateTime:  node.NodeCreateTime,
	}, nil
}

// getActualDocumentContent 获取实际文档内容 - 基于文档类型调用相应API
func (r *WikiContentRetriever) getActualDocumentContent(ctx context.Context, objToken, objType string) (string, error) {
	switch objType {
	case "doc", "docx":
		return r.extractDocContent(ctx, objToken)
	case "sheet", "sheets":
		return r.extractSheetContent(ctx, objToken)
	case "mindnote":
		return r.extractMindnoteContent(ctx, objToken)
	case "bitable":
		return r.extractBitableContent(ctx, objToken)
	default:
		return fmt.Sprintf("不支持的文档类型: %s", objType), nil
	}
}

// extractWikiToken 从Wiki URL中提取token
func (r *WikiContentRetriever) extractWikiToken(wikiUrl string) string {
	u, err := url.Parse(wikiUrl)
	if err != nil {
		return ""
	}

	pathArr := strings.Split(strings.Trim(u.Path, "/"), "/")
	if len(pathArr) >= 2 && pathArr[0] == "wiki" {
		return pathArr[1]
	}

	return ""
}

// extractDocContent 提取文档内容 - 基于官方文档API
func (r *WikiContentRetriever) extractDocContent(ctx context.Context, docToken string) (string, error) {
	// 使用官方文档API获取文档内容
	resp, err := r.apiClient.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/docx/v1/documents/%s/blocks", docToken),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
	})

	if err != nil {
		return "", fmt.Errorf("调用文档内容API失败: %w", err)
	}

	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Items []struct {
				BlockID   string `json:"block_id"`
				BlockType int    `json:"block_type"`
				Text      struct {
					Elements []struct {
						TextRun struct {
							Content string `json:"content"`
						} `json:"text_run"`
					} `json:"elements"`
				} `json:"text"`
			} `json:"items"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.RawBody, &result); err != nil {
		return "", fmt.Errorf("解析文档内容响应失败: %w", err)
	}

	if result.Code != 0 {
		return "", fmt.Errorf("获取文档内容失败: %s (code: %d)", result.Msg, result.Code)
	}

	var content strings.Builder
	for _, item := range result.Data.Items {
		for _, element := range item.Text.Elements {
			content.WriteString(element.TextRun.Content)
			content.WriteString(" ")
		}
		content.WriteString("\n")
	}

	return content.String(), nil
}

// extractSheetContent 提取表格内容 - 基于官方电子表格API
func (r *WikiContentRetriever) extractSheetContent(ctx context.Context, sheetToken string) (string, error) {
	// 首先获取工作表列表
	resp, err := r.apiClient.client.Do(ctx, &larkcore.ApiReq{
		HttpMethod:                http.MethodGet,
		ApiPath:                   fmt.Sprintf("/open-apis/sheets/v2/spreadsheets/%s/sheets_batch_get", sheetToken),
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
	})

	if err != nil {
		return "", fmt.Errorf("获取工作表列表失败: %w", err)
	}

	var sheetsResult struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Sheets []struct {
				SheetID string `json:"sheet_id"`
				Title   string `json:"title"`
			} `json:"sheets"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.RawBody, &sheetsResult); err != nil {
		return "", fmt.Errorf("解析工作表列表失败: %w", err)
	}

	if sheetsResult.Code != 0 {
		return "", fmt.Errorf("获取工作表列表失败: %s", sheetsResult.Msg)
	}

	var content strings.Builder
	content.WriteString("电子表格内容:\n")

	// 读取每个工作表的数据（限制前3个工作表）
	maxSheets := 3
	for i, sheet := range sheetsResult.Data.Sheets {
		if i >= maxSheets {
			break
		}

		// 读取工作表数据 - 基于官方API文档
		dataResp, err := r.apiClient.client.Do(ctx, &larkcore.ApiReq{
			HttpMethod:                http.MethodGet,
			ApiPath:                   fmt.Sprintf("/open-apis/sheets/v2/spreadsheets/%s/values/%s!A1:Z100", sheetToken, sheet.SheetID),
			SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
		})

		if err != nil {
			r.logger.Errorf("读取工作表 %s 数据失败: %v", sheet.Title, err)
			continue
		}

		var dataResult struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
			Data struct {
				ValueRange struct {
					Range  string          `json:"range"`
					Values [][]interface{} `json:"values"`
				} `json:"valueRange"`
			} `json:"data"`
		}

		if err := json.Unmarshal(dataResp.RawBody, &dataResult); err != nil {
			r.logger.Errorf("解析工作表 %s 数据失败: %v", sheet.Title, err)
			continue
		}

		if dataResult.Code != 0 {
			r.logger.Errorf("获取工作表 %s 数据失败: %s", sheet.Title, dataResult.Msg)
			continue
		}

		content.WriteString(fmt.Sprintf("\n工作表: %s\n", sheet.Title))

		// 处理表格数据（限制前10行）
		maxRows := 10
		for rowIdx, row := range dataResult.Data.ValueRange.Values {
			if rowIdx >= maxRows {
				content.WriteString("...(更多行数据)\n")
				break
			}

			var rowData []string
			for _, cell := range row {
				if cell != nil {
					rowData = append(rowData, fmt.Sprintf("%v", cell))
				} else {
					rowData = append(rowData, "")
				}
			}
			content.WriteString(strings.Join(rowData, " | "))
			content.WriteString("\n")
		}
	}

	return content.String(), nil
}

// extractMindnoteContent 提取思维导图内容
func (r *WikiContentRetriever) extractMindnoteContent(ctx context.Context, mindnoteToken string) (string, error) {
	// 思维导图内容提取 - 基于飞书开放平台文档API
	// 由于思维导图API相对复杂，这里提供基础的内容获取实现

	// 构建API请求URL - 获取思维导图基本信息
	apiURL := fmt.Sprintf("/open-apis/mindnote/v1/mindnotes/%s", mindnoteToken)

	// 发起API请求获取思维导图基本信息
	response, err := r.apiClient.Get(ctx, apiURL, nil)
	if err != nil {
		r.logger.Errorf("获取思维导图信息失败: %v", err)
		return fmt.Sprintf("思维导图文档 (Token: %s) - 无法获取详细内容", mindnoteToken), nil
	}

	// 解析响应获取思维导图标题和基本信息
	var mindnoteInfo struct {
		Data struct {
			Title string `json:"title"`
		} `json:"data"`
	}

	if err := json.Unmarshal(response, &mindnoteInfo); err != nil {
		r.logger.Errorf("解析思维导图信息失败: %v", err)
		return fmt.Sprintf("思维导图文档 (Token: %s)", mindnoteToken), nil
	}

	// 返回思维导图的基本信息
	// 注意：完整的思维导图内容提取需要更复杂的API调用来获取节点结构
	return fmt.Sprintf("思维导图: %s\n类型: 思维导图文档\nToken: %s\n\n注意: 思维导图的详细节点内容需要专门的API来获取完整结构",
		mindnoteInfo.Data.Title, mindnoteToken), nil
}

// extractBitableContent 提取多维表格内容
func (r *WikiContentRetriever) extractBitableContent(ctx context.Context, bitableToken string) (string, error) {
	// 多维表格API相对复杂，这里提供基础实现
	return fmt.Sprintf("多维表格 (Token: %s) - 详细内容提取需要专门的多维表格API", bitableToken), nil
}

// WikiNodeInfo Wiki节点信息结构
type WikiNodeInfo struct {
	SpaceID         string `json:"space_id"`
	NodeToken       string `json:"node_token"`
	ObjToken        string `json:"obj_token"`
	ObjType         string `json:"obj_type"`
	ParentToken     string `json:"parent_node_token"`
	NodeType        string `json:"node_type"`
	OriginNodeToken string `json:"origin_node_token"`
	OriginSpaceID   string `json:"origin_space_id"`
	HasChild        bool   `json:"has_child"`
	Title           string `json:"title"`
	ObjCreateTime   string `json:"obj_create_time"`
	ObjEditTime     string `json:"obj_edit_time"`
	NodeCreateTime  string `json:"node_create_time"`
}

// URLContentRetriever URL内容检索器，用于检测和处理文本中的链接
type URLContentRetriever struct {
	wikiRetriever *WikiContentRetriever
	logger        logx.Logger
}

// NewURLContentRetriever 创建URL内容检索器
func NewURLContentRetriever(wikiRetriever *WikiContentRetriever, logger logx.Logger) *URLContentRetriever {
	return &URLContentRetriever{
		wikiRetriever: wikiRetriever,
		logger:        logger,
	}
}

// CanRetrieve 判断是否能检索该类型的内容
func (r *URLContentRetriever) CanRetrieve(contentType string, source interface{}) bool {
	return contentType == "url" || contentType == "link"
}

// Retrieve 检索URL内容
func (r *URLContentRetriever) Retrieve(ctx context.Context, source interface{}) (*ContentResult, error) {
	text, ok := source.(string)
	if !ok {
		return nil, ErrInvalidSource
	}

	// 检测飞书Wiki链接
	if wikiUrl := r.detectFeishuWiki(text); wikiUrl != "" {
		return r.wikiRetriever.Retrieve(ctx, wikiUrl)
	}

	// 可以扩展支持其他类型的链接
	return nil, fmt.Errorf("不支持的URL类型")
}

// GetType 获取检索器类型
func (r *URLContentRetriever) GetType() string {
	return "url"
}

// detectFeishuWiki 检测飞书Wiki链接
func (r *URLContentRetriever) detectFeishuWiki(text string) string {
	if !strings.Contains(text, "https://boke.feishu.cn/wiki/") {
		return ""
	}

	re := regexp.MustCompile(`https://boke\.feishu\.cn/wiki/[a-zA-Z0-9-]+`)
	return re.FindString(text)
}
