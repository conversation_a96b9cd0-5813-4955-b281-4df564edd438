package bot

import (
	"context"
	"time"
)

// Bot 定义了通用的机器人接口，抽象了不同平台机器人的通用操作
type Bot interface {
	// Initialize 初始化机器人，建立连接和配置
	Initialize(ctx context.Context) error

	// HandleEvent 处理来自平台的事件
	HandleEvent(ctx context.Context, event *Event) error

	// SendTextMessage 发送文本消息
	SendTextMessage(ctx context.Context, chatType, chatId, text string) error

	// SendInteractiveMessage 发送交互式消息（如卡片）
	SendInteractiveMessage(ctx context.Context, messageId string, cardData *CardData) error

	// ReplyEmojiMessage 回复表情消息
	ReplyEmojiMessage(ctx context.Context, messageId string, emoji string) error

	// GetHistoryMessages 获取历史消息
	GetHistoryMessages(ctx context.Context, chatId string, startTime, endTime time.Time, limit int) (*HistoryMessage, error)

	// GetMessage 获取指定消息内容
	GetMessage(ctx context.Context, messageId string) (*FileData, string, error)

	// Cleanup 清理资源
	Cleanup() error

	// GetPlatform 获取机器人平台类型
	GetPlatform() BotPlatform

	// IsHealthy 检查机器人健康状态
	IsHealthy() bool
}

// BotPlatform 定义机器人平台类型
type BotPlatform string

const (
	PlatformFeishu   BotPlatform = "feishu"
	PlatformWeChat   BotPlatform = "wechat"
	PlatformDingTalk BotPlatform = "dingtalk"
)

// Event 表示来自机器人平台的事件
type Event struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
	Platform  BotPlatform `json:"platform"`
}

// ImMessageReceiveEvent 表示接收到的即时消息事件
type ImMessageReceiveEvent struct {
	Message *Message `json:"message"`
	Sender  *Sender  `json:"sender"`
}

// Message 表示消息内容
type Message struct {
	MessageID   string     `json:"message_id"`
	MessageType string     `json:"message_type"`
	ChatType    string     `json:"chat_type"`
	ChatID      string     `json:"chat_id"`
	Content     string     `json:"content"`
	CreateTime  string     `json:"create_time"`
	ParentID    string     `json:"parent_id,omitempty"`
	Mentions    []*Mention `json:"mentions,omitempty"`
}

// Sender 表示消息发送者
type Sender struct {
	SenderID   *SenderID `json:"sender_id"`
	SenderType string    `json:"sender_type"`
	TenantKey  string    `json:"tenant_key"`
}

// SenderID 表示发送者ID
type SenderID struct {
	OpenID  string `json:"open_id"`
	UserID  string `json:"user_id,omitempty"`
	UnionID string `json:"union_id,omitempty"`
}

// Mention 表示@提及信息
type Mention struct {
	Key       string `json:"key"`
	ID        *ID    `json:"id"`
	Name      string `json:"name"`
	TenantKey string `json:"tenant_key"`
}

// ID 表示用户ID信息
type ID struct {
	OpenID  string `json:"open_id"`
	UserID  string `json:"user_id,omitempty"`
	UnionID string `json:"union_id,omitempty"`
}

// P2PJoinChat 表示P2P聊天加入事件
type P2PJoinChat struct {
	OperatorId *ID `json:"operator_id"`
}

// FileData 表示文件数据
type FileData struct {
	FileKey   string `json:"file_key"`
	FileName  string `json:"file_name"`
	FileType  string `json:"file_type,omitempty"`
	FileSize  int64  `json:"file_size,omitempty"`
	WikiType  string `json:"wiki_type,omitempty"`
	MessageId string `json:"message_id,omitempty"`
}

// TextData 表示文本数据
type TextData struct {
	Text      string `json:"text"`
	MessageId string `json:"message_id,omitempty"`
}

// HistoryMessage 表示历史消息
type HistoryMessage struct {
	Files []*FileData `json:"files"`
	Texts []*TextData `json:"texts"`
}

// CardData 表示卡片数据
type CardData struct {
	TemplateID          string         `json:"template_id"`
	TemplateVersionName string         `json:"template_version_name"`
	TemplateVariable    map[string]any `json:"template_variable"`
}

// Card 表示卡片消息
type Card struct {
	Type string    `json:"type"`
	Data *CardData `json:"data"`
}

// WorkFlowRes 表示工作流响应
type WorkFlowRes struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// BotConfig 表示机器人配置接口
type BotConfig interface {
	// GetAppID 获取应用ID
	GetAppID() string

	// GetAppSecret 获取应用密钥
	GetAppSecret() string

	// GetBotName 获取机器人名称
	GetBotName() string

	// GetWebhookURL 获取Webhook URL
	GetWebhookURL() string

	// Validate 验证配置是否有效
	Validate() error
}

// EventHandler 定义事件处理器接口
type EventHandler interface {
	// HandleMessage 处理消息事件
	HandleMessage(ctx context.Context, event *Event) error

	// HandleJoinChat 处理加入聊天事件
	HandleJoinChat(ctx context.Context, event *Event) error

	// HandleLeaveChat 处理离开聊天事件
	HandleLeaveChat(ctx context.Context, event *Event) error

	// HandleUnknownEvent 处理未知事件
	HandleUnknownEvent(ctx context.Context, event *Event) error
}

// MessageProcessor 定义消息处理器接口
type MessageProcessor interface {
	// ProcessTextMessage 处理文本消息
	ProcessTextMessage(ctx context.Context, message *ImMessageReceiveEvent) error

	// ProcessFileMessage 处理文件消息
	ProcessFileMessage(ctx context.Context, message *ImMessageReceiveEvent) error

	// ProcessImageMessage 处理图片消息
	ProcessImageMessage(ctx context.Context, message *ImMessageReceiveEvent) error
}

// BotManager 定义机器人管理器接口
type BotManager interface {
	// RegisterBot 注册机器人
	RegisterBot(platform BotPlatform, bot Bot) error

	// GetBot 获取指定平台的机器人
	GetBot(platform BotPlatform) (Bot, error)

	// GetAllBots 获取所有注册的机器人
	GetAllBots() map[BotPlatform]Bot

	// StartAll 启动所有机器人
	StartAll(ctx context.Context) error

	// StopAll 停止所有机器人
	StopAll() error
}
