package mq

import (
	"encoding/json"
	"fmt"
	"github.com/nsqio/go-nsq"
	"github.com/spf13/cast"
	"log"
	"time"
)

// 初始化生产者
func InitProducer(nsqd string) *nsq.Producer {
	config := nsq.NewConfig()
	producer, err := nsq.NewProducer(nsqd, config)
	if err != nil {
		log.Fatal(err)
		return nil
	}
	return producer
}

// 初始化消费者
func InitConsumer(lookupd string, topic string, consumerNum int8, handler nsq.Handler) {
	config := nsq.NewConfig()
	config.LookupdPollInterval = 15 * time.Second
	for i := int8(0); i < consumerNum; i++ {
		c, err := nsq.NewConsumer(topic, fmt.Sprintf("%s-1", topic), config)
		if err != nil {
			log.Fatal(err)
			return
		}
		c.<PERSON><PERSON>ogger<PERSON>evel(nsq.LogLevelWarning)
		c.Add<PERSON>andler(handler)
		err = c.ConnectToNSQLookupd(lookupd)
		if err != nil {
			log.Fatal(err)
			return
		}
	}
}

func Publish(producer *nsq.Producer, topic string, body any) error {
	var pushData []byte
	switch body.(type) {
	case string:
		pushData = []byte(cast.ToString(body))
	case int8, int16, int32, int64, int, uint8, uint16, uint32, uint64, uint, float32, float64:
		pushData = []byte(cast.ToString(body))
	default:
		b, err := json.Marshal(body)
		if err != nil {
			return err
		}
		pushData = b
	}
	log.Printf("topic:%s,body:%s", topic, pushData)
	return producer.Publish(topic, pushData)
}
