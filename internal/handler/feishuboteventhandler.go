package handler

import (
	"net/http"

	"github.com/run-bigpig/hongdou/internal/logic"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 飞书机器人事件
func FeiShuBotEventHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.FeiShuBotReceiveEventRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewFeiShuBotEventLogic(r.Context(), svcCtx)
		resp, err := l.FeiShuBotEvent(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
