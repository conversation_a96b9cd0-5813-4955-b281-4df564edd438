// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package handler

import (
	"net/http"

	"github.com/run-bigpig/hongdou/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 聊天 - Unified endpoint supporting both streaming and non-streaming responses
				Method:  http.MethodPost,
				Path:    "/chat",
				Handler: ChatHandler(serverCtx),
			},
			{
				// 飞书机器人事件
				Method:  http.MethodPost,
				Path:    "/feishu_bot_event",
				Handler: FeiShuBotEventHandler(serverCtx),
			},
			{
				// 保存长期记忆
				Method:  http.MethodPost,
				Path:    "/save_memory",
				Handler: SaveMemoryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1"),
	)
}
